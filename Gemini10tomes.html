<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulgarian Roguelike - Ten Tomes of Wisdom</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=MedievalSharp&family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0d1117; 
            color: #c9d1d9; 
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 1rem;
        }

        .game-title-main {
            font-family: 'MedievalSharp', cursive;
            font-size: 3rem; 
            color: #79c0ff; 
            text-shadow: 0 0 10px #79c0ff, 0 0 15px #79c0ff;
            margin-bottom: 1.5rem;
        }

        .game-setup-container, .game-container {
            background-color: #161b22; 
            padding: 1.5rem; /* Slightly reduced padding for more options */
            border-radius: 0.75rem; 
            border: 1px solid #30363d; 
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 700px; /* Wider for more options */
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .mage-image-container {
            margin-bottom: 1rem; /* Reduced margin */
            display: flex;
            justify-content: center;
        }
        .mage-image {
            width: 80px; /* Smaller mage */
            height: 80px;
            border-radius: 50%;
            border: 3px solid #79c0ff; 
            box-shadow: 0 0 15px #79c0ff;
            object-fit: cover;
        }

        .setup-section {
            background-color: #0d1117; 
            padding: 1rem; /* Reduced padding */
            border-radius: 0.5rem;
            margin-bottom: 0.75rem; /* Reduced margin */
            border: 1px solid #30363d;
        }

        .setup-section h2 {
            font-family: 'MedievalSharp', cursive;
            color: #a371f7; 
            font-size: 1.2rem; /* Adjusted size */
            margin-bottom: 0.5rem; /* Reduced margin */
        }

        .setup-options {
            display: grid; /* Use grid for better layout of many options */
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Responsive columns */
            gap: 0.5rem; 
        }
        
        .setup-options label {
            display: block; 
            background-color: #21262d;
            color: #c9d1d9;
            padding: 0.5rem 1rem; /* Adjusted padding */
            border-radius: 0.375rem;
            font-weight: 500; 
            font-size: 0.875rem; /* Slightly smaller font for more options */
            transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
            border: 1px solid #30363d;
            cursor: pointer;
            text-align: left; 
        }
        
        .setup-options input[type="radio"] {
            margin-right: 0.4rem;
            accent-color: #79c0ff; 
        }

        .setup-options label:hover {
            background-color: #30363d;
            color: #79c0ff;
            box-shadow: 0 0 8px rgba(121, 192, 255, 0.5);
        }
        
         .setup-options label.selected-option {
            background-color: #58a6ff;
            color: #0d1117;
            border-color: #79c0ff;
            box-shadow: 0 0 10px rgba(88, 166, 255, 0.7);
        }
        #word-set-selection-container.hidden { 
            display: none !important;
        }


        #start-game-button {
            background: linear-gradient(145deg, #56d364, #30a14e); 
            color: white;
            padding: 0.85rem 2.5rem;
            margin-top: 1.5rem;
            border-radius: 0.375rem;
            font-weight: bold;
            font-size: 1.1rem;
            border: none;
            box-shadow: 0 4px 10px rgba(86, 211, 100, 0.4);
            transition: transform 0.1s, box-shadow 0.2s;
        }
        #start-game-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(86, 211, 100, 0.6);
        }
        
        .game-controls {
            display: flex;
            justify-content: center;
            margin-top: 1rem;
        }
        #exit-game-button {
            background-color: #ef4444; 
            color: white;
            padding: 0.6rem 1.2rem;
            border-radius: 0.375rem;
            font-weight: 600;
            transition: background-color 0.2s;
            border: 1px solid #dc2626; 
        }
        #exit-game-button:hover {
            background-color: #dc2626; 
        }


        .stats-bar { display: flex; justify-content: space-around; margin-bottom: 1.5rem; font-size: 0.9rem; }
        .stat { background-color: #21262d; padding: 0.6rem 1.2rem; border-radius: 0.375rem; border: 1px solid #30363d; box-shadow: inset 0 1px 3px rgba(0,0,0,0.5); }
        .stat span { color: #79c0ff; font-weight: bold; }

        .progress-bar-container { width: 100%; background-color: #21262d; border-radius: 10px; margin-bottom: 2rem; height: 24px; overflow: hidden; border: 1px solid #30363d; box-shadow: inset 0 2px 4px rgba(0,0,0,0.4); }
        .progress-bar-fill { height: 100%; width: 0%; background: linear-gradient(90deg, #a371f7, #79c0ff); border-radius: 8px; transition: width 0.4s ease-in-out; display: flex; align-items: center; justify-content: center; font-size: 0.8rem; font-weight: bold; color: #ffffff; text-shadow: 0 0 3px rgba(0,0,0,0.7); }
        
        #english-word-container { min-height: 80px; }
        #english-word { font-family: 'MedievalSharp', cursive; font-size: 2.5rem; color: #f78166; margin-bottom: 1.5rem; text-shadow: 0 0 8px #f78166; }
        
        .input-area { display: flex; gap: 0.75rem; margin-bottom: 1rem; align-items: stretch; }
        .input-area input { flex-grow: 1; padding: 0.85rem; border-radius: 0.375rem; border: 1px solid #30363d; background-color: #0d1117; color: #c9d1d9; font-size: 1rem; }
        .input-area input:focus { outline: none; border-color: #58a6ff; box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.3); }
        .input-area button { padding: 0.85rem 1.2rem; border-radius: 0.375rem; color: white; font-weight: 600; border: none; cursor: pointer; transition: background-color 0.2s, transform 0.1s; }
        #submit-button { background-color: #58a6ff; }
        #submit-button:hover { background-color: #79c0ff; transform: translateY(-1px); }
        #hint-button { background-color: #f78166; }
        #hint-button:hover { background-color: #fdac95; transform: translateY(-1px); }
        #hint-button:disabled { background-color: #484f58; cursor: not-allowed; transform: translateY(0); }

        .feedback-area { color: #f0f6fc; }
        #feedback-message.correct { color: #56d364; font-weight: bold; }
        #feedback-message.incorrect { color: #f85149; font-weight: bold; }
        #hint-text { color: #e3b341; margin-top: 0.75rem; font-style: italic; }

        .game-over-message { font-family: 'MedievalSharp', cursive; font-size: 2rem; margin-top: 2rem; margin-bottom: 1rem; }
        .game-over-message.win { color: #56d364; text-shadow: 0 0 10px #56d364;}
        .game-over-message.lose { color: #f85149; text-shadow: 0 0 10px #f85149;}
        #restart-button { background-color: #a371f7; color: white; padding: 0.75rem 1.5rem; font-family: 'MedievalSharp', cursive; font-size: 1.2rem; }
        #restart-button:hover { background-color: #b792f9; }
        .hidden { display: none; }

        @media (max-width: 520px) { 
            .game-title-main { font-size: 2.2rem; }
            .input-area { flex-direction: column; }
            .input-area input { width: 100%; margin-bottom: 0.5rem; }
            .input-area button { width: 100%; }
            #hint-button { margin-top: 0.5rem; }
            .mage-image { width: 80px; height: 80px; }
            #english-word { font-size: 2rem; }
            .setup-options { grid-template-columns: 1fr; } /* Stack options on small screens */
        }
    </style>
</head>
<body>
    <h1 id="main-title" class="game-title-main">Bulgarian Roguelike</h1>

    <div id="game-setup-container" class="game-setup-container">
        <div class="mage-image-container">
            <img src="https://placehold.co/80x80/0d1117/79c0ff?text=Mage&font=medievalsharp" alt="Hooded Mage" class="mage-image">
        </div>
        
        <div class="setup-section">
            <h2>Choose Your Path, Learner:</h2>
            <div class="setup-options difficulty-buttons">
                <label><input type="radio" name="difficulty" value="easy"> <span>Easy Path (3 HP, 3 Hints)</span></label>
                <label><input type="radio" name="difficulty" value="medium" checked> <span>Medium Path (2 HP, 2 Hints)</span></label>
                <label><input type="radio" name="difficulty" value="hard"> <span>Hard Path (1 HP, 1 Hint)</span></label>
            </div>
        </div>

        <div class="setup-section">
            <h2>Scrolls of Repetition:</h2>
            <div class="setup-options word-order-buttons">
                <label><input type="radio" name="wordOrder" value="random" checked> <span>Randomized Scrolls</span></label>
                <label><input type="radio" name="wordOrder" value="fixed"> <span>Fixed Scrolls</span></label>
            </div>
        </div>

        <div class="setup-section">
            <h2>Focus of Study:</h2>
            <div class="setup-options content-type-buttons">
                <label><input type="radio" name="contentType" value="words" checked> <span>Ancient Words</span></label>
                <label><input type="radio" name="contentType" value="sentences"> <span>Sacred Sentences</span></label>
            </div>
        </div>

        <div id="word-set-selection-container" class="setup-section"> 
            <h2>Choose Your Tome of Words:</h2>
            <div class="setup-options word-set-buttons">
                <label><input type="radio" name="wordSet" value="core" checked> <span>Tome 1: Core Vocabulary</span></label>
                <label><input type="radio" name="wordSet" value="actions"> <span>Tome 2: Actions & Verbs</span></label>
                <label><input type="radio" name="wordSet" value="people"> <span>Tome 3: People & Places</span></label>
                <label><input type="radio" name="wordSet" value="food"> <span>Tome 4: Food & Drink</span></label>
                <label><input type="radio" name="wordSet" value="travel"> <span>Tome 5: Travel & Directions</span></label>
                <label><input type="radio" name="wordSet" value="nature"> <span>Tome 6: Nature & Animals</span></label>
                <label><input type="radio" name="wordSet" value="hobbies"> <span>Tome 7: Hobbies & Leisure</span></label>
                <label><input type="radio" name="wordSet" value="tech"> <span>Tome 8: Technology & Internet</span></label>
                <label><input type="radio" name="wordSet" value="emotions"> <span>Tome 9: Emotions & Feelings</span></label>
                <label><input type="radio" name="wordSet" value="concepts"> <span>Tome 10: Abstract Concepts</span></label>
            </div>
        </div>


        <button id="start-game-button">Begin Quest!</button>
        <p id="difficulty-feedback" class="text-sm text-red-400 mt-2"></p>
    </div>

    <div id="game-main-container" class="game-container hidden">
        <div class="stats-bar">
            <div class="stat">HP: <span id="player-hp">3</span></div>
            <div class="stat">Score: <span id="player-score">0</span></div>
            <div class="stat">Floor: <span id="current-floor">1</span>/<span id="total-floors">0</span></div>
        </div>

        <div class="progress-bar-container">
            <div id="progress-bar-fill" class="progress-bar-fill">0%</div>
        </div>

        <div id="english-word-container" class="encounter-area">
            <p id="encounter-prompt" class="text-lg mb-2 text-gray-400">Decipher the ancient script (English to Bulgarian):</p>
            <p id="english-word">Word/Sentence</p>
        </div>

        <div class="input-area">
            <input type="text" id="translation-input" placeholder="Inscribe Bulgarian translation...">
            <button id="submit-button">Submit</button>
            <button id="hint-button">✨ Seek Wisdom (Hint)</button>
        </div>
        
        <div class="game-controls">
            <button id="exit-game-button">Exit Quest</button>
        </div>

        <div class="feedback-area">
            <p id="feedback-message"></p>
        </div>
        <div class="hint-display-area">
            <p id="hint-text"></p>
        </div>

        <div id="game-over-container" class="hidden mt-8">
            <p id="game-over-message" class="game-over-message"></p>
            <button id="restart-button" class="mt-4 px-6 py-2 rounded text-white font-semibold transition">Seek Another Path?</button>
        </div>
    </div>

    <script>
        // --- Word & Sentence Lists ---
        const wordSet_CoreVocabulary = [
            // ... (Expanded to ~100 as in previous response)
            { en: "yes", bg: "да" }, { en: "no", bg: "не" },
            { en: "hello", bg: "здравей" }, { en: "goodbye", bg: "довиждане" },
            { en: "thank you", bg: "благодаря" }, { en: "please", bg: "моля" },
            { en: "sorry", bg: "съжалявам" }, { en: "water", bg: "вода" },
            { en: "food", bg: "храна" }, { en: "house", bg: "къща" },
            { en: "car", bg: "кола" }, { en: "book", bg: "книга" },
            { en: "friend", bg: "приятел" }, { en: "family", bg: "семейство" },
            { en: "time", bg: "време" }, { en: "day", bg: "ден" },
            { en: "night", bg: "нощ" }, { en: "morning", bg: "сутрин" },
            { en: "evening", bg: "вечер" }, { en: "today", bg: "днес" },
            { en: "tomorrow", bg: "утре" }, { en: "yesterday", bg: "вчера" },
            { en: "money", bg: "пари" }, { en: "shop", bg: "магазин" },
            { en: "name", bg: "име" }, { en: "city", bg: "град" },
            { en: "country", bg: "държава" }, { en: "street", bg: "улица" },
            { en: "number", bg: "число" }, { en: "color", bg: "цвят" },
            { en: "question", bg: "въпрос" }, { en: "answer", bg: "отговор" },
            { en: "problem", bg: "проблем" }, { en: "help", bg: "помощ" },
            { en: "idea", bg: "идея" }, { en: "example", bg: "пример" },
            { en: "thing", bg: "нещо" }, { en: "part", bg: "част" },
            { en: "place", bg: "място" }, { en: "way", bg: "начин" },
            { en: "man", bg: "мъж" }, { en: "woman", bg: "жена" },
            { en: "child", bg: "дете" }, { en: "people", bg: "хора" },
            { en: "world", bg: "свят" }, { en: "life", bg: "живот" },
            { en: "hand", bg: "ръка" }, { en: "eye", bg: "око" },
            { en: "head", bg: "глава" }, { en: "heart", bg: "сърце" },
            { en: "sun", bg: "слънце" }, { en: "moon", bg: "луна" },
            { en: "star", bg: "звезда" }, { en: "sky", bg: "небе" },
            { en: "earth", bg: "земя" }, { en: "fire", bg: "огън" },
            { en: "air", bg: "въздух" }, { en: "tree", bg: "дърво" },
            { en: "flower", bg: "цвете" }, { en: "animal", bg: "животно" },
            { en: "dog", bg: "куче" }, { en: "cat", bg: "котка" },
            { en: "bird", bg: "птица" }, { en: "fish", bg: "риба" },
            { en: "language", bg: "език" }, { en: "word", bg: "дума" },
            { en: "letter", bg: "буква" }, { en: "story", bg: "история" },
            { en: "music", bg: "музика" }, { en: "game", bg: "игра" },
            { en: "school", bg: "училище" }, { en: "student", bg: "ученик" },
            { en: "teacher", bg: "учител" }, { en: "doctor", bg: "лекар" },
            { en: "job", bg: "работа" }, { en: "end", bg: "край" }, 
            { en: "beginning", bg: "начало" }, { en: "love", bg: "любов" },
            { en: "year", bg: "година" }, { en: "month", bg: "месец" },
            { en: "week", bg: "седмица" }, { en: "hour", bg: "час" },
            { en: "minute", bg: "минута" }, { en: "second", bg: "секунда" },
            { en: "body", bg: "тяло" }, { en: "foot", bg: "крак" }, 
            { en: "leg", bg: "крак" }, { en: "arm", bg: "ръка" }, 
            { en: "face", bg: "лице" }, { en: "hair", bg: "коса" },
            { en: "mouth", bg: "уста" }, { en: "nose", bg: "нос" },
            { en: "ear", bg: "ухо" }, { en: "back", bg: "гръб" },
            { en: "door", bg: "врата" }, { en: "window", bg: "прозорец" },
            { en: "table", bg: "маса" }, { en: "chair", bg: "стол" },
            { en: "bed", bg: "легло" }, { en: "room", bg: "стая" },
            { en: "key", bg: "ключ" }, { en: "paper", bg: "хартия" },
            { en: "pen", bg: "химикал" }, { en: "pencil", bg: "молив" },
            { en: "phone", bg: "телефон" }, { en: "computer", bg: "компютър" },
            { en: "light", bg: "светлина" }, { en: "sound", bg: "звук" },
            { en: "price", bg: "цена" }, { en: "size", bg: "размер" },
            { en: "reason", bg: "причина" }, { en: "result", bg: "резултат" },
            { en: "fact", bg: "факт" }, { en: "news", bg: "новини" },
            { en: "art", bg: "изкуство" }, { en: "war", bg: "война" },
            { en: "peace", bg: "мир" }, { en: "history", bg: "история" },
            { en: "map", bg: "карта" }, { en: "weather", bg: "време" }, 
            { en: "rain", bg: "дъжд" }, { en: "snow", bg: "сняг" },
            { en: "wind", bg: "вятър" }, { en: "cloud", bg: "облак" },
            { en: "road", bg: "път" }, { en: "path", bg: "пътека" },
            { en: "information", bg: "информация" }, { en: "system", bg: "система" },
            { en: "program", bg: "програма" }, { en: "government", bg: "правителство" },
            { en: "company", bg: "компания" }, { en: "group", bg: "група" },
            { en: "member", bg: "член" }, { en: "service", bg: "услуга" }
        ];
        const wordSet_ActionsVerbs = [ /* Approx 100 verbs */
            { en: "be", bg: "съм" }, { en: "have", bg: "имам" }, { en: "do", bg: "правя" }, { en: "say", bg: "казвам" }, { en: "go", bg: "отивам" }, { en: "get", bg: "получавам" }, { en: "make", bg: "правя" }, { en: "know", bg: "знам" }, { en: "think", bg: "мисля" }, { en: "see", bg: "виждам" }, { en: "come", bg: "идвам" }, { en: "want", bg: "искам" }, { en: "look", bg: "гледам" }, { en: "use", bg: "използвам" }, { en: "find", bg: "намирам" }, { en: "give", bg: "давам" }, { en: "tell", bg: "казвам" }, { en: "work", bg: "работя" }, { en: "call", bg: "обаждам се" }, { en: "try", bg: "опитвам" }, { en: "ask", bg: "питам" }, { en: "need", bg: "нуждая се" }, { en: "feel", bg: "чувствам" }, { en: "become", bg: "ставам" }, { en: "leave", bg: "напускам" }, { en: "put", bg: "слагам" }, { en: "mean", bg: "означава" }, { en: "keep", bg: "пазя" }, { en: "let", bg: "позволявам" }, { en: "begin", bg: "започвам" }, { en: "seem", bg: "изглежда" }, { en: "help", bg: "помагам" }, { en: "talk", bg: "говоря" }, { en: "turn", bg: "завивам" }, { en: "start", bg: "стартирам" }, { en: "show", bg: "показвам" }, { en: "hear", bg: "чувам" }, { en: "play", bg: "играя" }, { en: "run", bg: "тичам" }, { en: "move", bg: "движа се" }, { en: "like", bg: "харесвам" }, { en: "live", bg: "живея" }, { en: "believe", bg: "вярвам" }, { en: "hold", bg: "държа" }, { en: "bring", bg: "нося" }, { en: "happen", bg: "случва се" }, { en: "write", bg: "пиша" }, { en: "provide", bg: "осигурявам" }, { en: "sit", bg: "седя" }, { en: "stand", bg: "стоя" }, { en: "lose", bg: "губя" }, { en: "pay", bg: "плащам" }, { en: "meet", bg: "срещам" }, { en: "include", bg: "включвам" }, { en: "continue", bg: "продължавам" }, { en: "set", bg: "задавам" }, { en: "learn", bg: "уча" }, { en: "change", bg: "променям" }, { en: "lead", bg: "водя" }, { en: "understand", bg: "разбирам" }, { en: "watch", bg: "гледам" }, { en: "follow", bg: "следвам" }, { en: "stop", bg: "спирам" }, { en: "create", bg: "създавам" }, { en: "speak", bg: "говоря" }, { en: "read", bg: "чета" }, { en: "allow", bg: "позволявам" }, { en: "add", bg: "добавям" }, { en: "spend", bg: "харча" }, { en: "grow", bg: "расна" }, { en: "open", bg: "отварям" }, { en: "walk", bg: "вървя" }, { en: "win", bg: "печеля" }, { en: "offer", bg: "предлагам" }, { en: "remember", bg: "помня" }, { en: "love", bg: "обичам" }, { en: "consider", bg: "обмислям" }, { en: "appear", bg: "появявам се" }, { en: "buy", bg: "купувам" }, { en: "wait", bg: "чакам" }, { en: "serve", bg: "сервирам" }, { en: "die", bg: "умирам" }, { en: "send", bg: "изпращам" }, { en: "expect", bg: "очаквам" }, { en: "build", bg: "строя" }, { en: "stay", bg: "оставам" }, { en: "fall", bg: "падам" }, { en: "cut", bg: "режа" }, { en: "reach", bg: "достигам" }, { en: "kill", bg: "убивам" }, { en: "raise", bg: "повдигам" }, { en: "pass", bg: "подавам" }, { en: "sell", bg: "продавам" }, { en: "decide", bg: "решавам" }, { en: "return", bg: "връщам се" }, { en: "explain", bg: "обяснявам" }, { en: "hope", bg: "надявам се" }, { en: "develop", bg: "развивам" }, { en: "carry", bg: "нося" }, { en: "break", bg: "чупя" }, { en: "receive", bg: "получавам" }, { en: "agree", bg: "съгласявам се" }, { en: "support", bg: "подкрепям" }, { en: "hit", bg: "удрям" }, { en: "eat", bg: "ям" }, { en: "drink", bg: "пия" }, { en: "sleep", bg: "спя" }, { en: "drive", bg: "карам" }, { en: "fly", bg: "летя" }, { en: "sing", bg: "пея" }, { en: "dance", bg: "танцувам" }, { en: "swim", bg: "плувам" }, { en: "cook", bg: "готвя" }, { en: "clean", bg: "чистя" }, { en: "study", bg: "уча" }, { en: "close", bg: "затварям" }, { en: "choose", bg: "избирам" }, { en: "compare", bg: "сравнявам" }, { en: "complain", bg: "оплаквам се" }, { en: "complete", bg: "завършвам" }, { en: "confirm", bg: "потвърждавам" }, { en: "connect", bg: "свързвам" }, { en: "count", bg: "броя" }, { en: "cover", bg: "покривам" }, { en: "cry", bg: "плача" }, { en: "describe", bg: "описвам" }, { en: "destroy", bg: "унищожавам" }, { en: "discover", bg: "откривам" }, { en: "discuss", bg: "обсъждам" }, { en: "draw", bg: "рисувам" }, { en: "dream", bg: "сънувам" }, { en: "enjoy", bg: "наслаждавам се" }, { en: "enter", bg: "влизам" }, { en: "escape", bg: "избягвам" }, { en: "examine", bg: "изследвам" }, { en: "exist", bg: "съществувам" }, { en: "fight", bg: "бия се" }, { en: "fill", bg: "пълня" }, { en: "finish", bg: "свършвам" }, { en: "fix", bg: "поправям" }, { en: "forget", bg: "забравям" }, { en: "forgive", bg: "прощавам" }, { en: "guess", bg: "познавам" }, { en: "hate", bg: "мразя" }, { en: "imagine", bg: "представям си" }, { en: "invite", bg: "каня" }, { en: "join", bg: "присъединявам се" }, { en: "jump", bg: "скачам" }, { en: "laugh", bg: "смея се" }, { en: "listen", bg: "слушам" }
        ];
        const wordSet_PeoplePlacesDescriptions = [ /* Approx 100 nouns & adjectives */
            { en: "father", bg: "баща" }, { en: "mother", bg: "майка" }, { en: "brother", bg: "брат" }, { en: "sister", bg: "сестра" }, { en: "son", bg: "син" }, { en: "daughter", bg: "дъщеря" }, { en: "husband", bg: "съпруг" }, { en: "wife", bg: "съпруга" }, { en: "parent", bg: "родител" }, { en: "baby", bg: "бебе" }, { en: "boy", bg: "момче" }, { en: "girl", bg: "момиче" }, { en: "adult", bg: "възрастен" }, { en: "neighbor", bg: "съсед" }, { en: "president", bg: "президент" }, { en: "king", bg: "цар" }, { en: "queen", bg: "царица" }, { en: "artist", bg: "художник" }, { en: "writer", bg: "писател" }, { en: "musician", bg: "музикант" }, { en: "kitchen", bg: "кухня" }, { en: "bedroom", bg: "спалня" }, { en: "bathroom", bg: "баня" }, { en: "garden", bg: "градина" }, { en: "park", bg: "парк" }, { en: "library", bg: "библиотека" }, { en: "hospital", bg: "болница" }, { en: "airport", bg: "летище" }, { en: "station", bg: "гара" }, { en: "restaurant", bg: "ресторант" }, { en: "hotel", bg: "хотел" }, { en: "bank", bg: "банка" }, { en: "church", bg: "църква" }, { en: "mountain", bg: "планина" }, { en: "sea", bg: "море" }, { en: "river", bg: "река" }, { en: "forest", bg: "гора" }, { en: "road", bg: "път" }, { en: "bridge", bg: "мост" }, { en: "village", bg: "село" }, { en: "capital", bg: "столица" }, { en: "island", bg: "остров" }, { en: "office", bg: "офис" }, { en: "factory", bg: "фабрика" }, { en: "farm", bg: "ферма" }, { en: "market", bg: "пазар" }, { en: "theater", bg: "театър" }, { en: "museum", bg: "музей" }, { en: "university", bg: "университет" }, { en: "college", bg: "колеж" }, { en: "building", bg: "сграда" }, { en: "room", bg: "стая" }, { en: "square", bg: "площад" }, { en: "countryside", bg: "провинция" }, { en: "apartment", bg: "апартамент" }, { en: "floor", bg: "етаж" }, { en: "roof", bg: "покрив" }, { en: "wall", bg: "стена" }, { en: "corner", bg: "ъгъл" }, { en: "center", bg: "център" }, { en: "border", bg: "граница" }, { en: "area", bg: "зона" }, { en: "region", bg: "област" }, { en: "continent", bg: "континент" }, { en: "planet", bg: "планета" }, { en: "space", bg: "космос" }, { en: "big", bg: "голям" }, { en: "small", bg: "малък" }, { en: "good", bg: "добър" }, { en: "bad", bg: "лош" }, { en: "beautiful", bg: "красив" }, { en: "ugly", bg: "грозен" }, { en: "happy", bg: "щастлив" }, { en: "sad", bg: "тъжен" }, { en: "new", bg: "нов" }, { en: "old", bg: "стар" }, { en: "young", bg: "млад" }, { en: "rich", bg: "богат" }, { en: "poor", bg: "беден" }, { en: "hot", bg: "горещ" }, { en: "cold", bg: "студен" }, { en: "warm", bg: "топъл" }, { en: "cool", bg: "хладен" }, { en: "easy", bg: "лесен" }, { en: "difficult", bg: "труден" }, { en: "fast", bg: "бърз" }, { en: "slow", bg: "бавен" }, { en: "long", bg: "дълъг" }, { en: "short", bg: "къс" }, { en: "high", bg: "висок" }, { en: "low", bg: "нисък" }, { en: "strong", bg: "силен" }, { en: "weak", bg: "слаб" }, { en: "heavy", bg: "тежък" }, { en: "light", bg: "лек" }, { en: "dark", bg: "тъмен" }, { en: "bright", bg: "светъл" }, { en: "clean", bg: "чист" }, { en: "dirty", bg: "мръсен" }, { en: "full", bg: "пълен" }, { en: "empty", bg: "празен" }, { en: "true", bg: "верен" }, { en: "false", bg: "грешен" }, { en: "important", bg: "важен" }, { en: "interesting", bg: "интересен" }, { en: "boring", bg: "скучен" }, { en: "famous", bg: "известен" }, { en: "red", bg: "червен" }, { en: "blue", bg: "син" }, { en: "green", bg: "зелен" }, { en: "yellow", bg: "жълт" }, { en: "white", bg: "бял" }, { en: "black", bg: "черен" }, { en: "brown", bg: "кафяв" }, { en: "gray", bg: "сив" }, { en: "orange", bg: "оранжев" }, { en: "purple", bg: "лилав" }, { en: "pink", bg: "розов" }, { en: "kind", bg: "мил" }, { en: "angry", bg: "ядосан" }, { en: "tired", bg: "уморен" }, { en: "hungry", bg: "гладен" }, { en: "thirsty", bg: "жаден" }, { en: "sick", bg: "болен" }, { en: "healthy", bg: "здрав" }, { en: "brave", bg: "смел" }, { en: "clever", bg: "умен" }, { en: "stupid", bg: "глупав" }, { en: "funny", bg: "забавен" }, { en: "serious", bg: "сериозен" }, { en: "different", bg: "различен" }, { en: "same", bg: "същият" }, { en: "possible", bg: "възможен" }, { en: "impossible", bg: "невъзможен" }, { en: "real", bg: "истински" }, { en: "sure", bg: "сигурен" }, { en: "free", bg: "свободен" }, { en: "busy", bg: "зает" }, { en: "ready", bg: "готов" }, { en: "late", bg: "късен" }, { en: "early", bg: "ранен" }, { en: "modern", bg: "модерен" }, { en: "traditional", bg: "традиционен" }
        ];
        const wordSet_FoodDrink = [ /* Approx 70 words */
            { en: "bread", bg: "хляб" }, { en: "cheese", bg: "сирене" }, { en: "milk", bg: "мляко" }, { en: "egg", bg: "яйце" }, { en: "meat", bg: "месо" }, { en: "chicken", bg: "пиле" }, { en: "pork", bg: "свинско" }, { en: "beef", bg: "телешко" }, { en: "lamb", bg: "агнешко" }, { en: "sausage", bg: "наденица" }, { en: "vegetable", bg: "зеленчук" }, { en: "fruit", bg: "плод" }, { en: "apple", bg: "ябълка" }, { en: "banana", bg: "банан" }, { en: "orange", bg: "портокал" }, { en: "grape", bg: "грозде" }, { en: "strawberry", bg: "ягода" }, { en: "tomato", bg: "домат" }, { en: "potato", bg: "картоф" }, { en: "onion", bg: "лук" }, { en: "garlic", bg: "чесън" }, { en: "carrot", bg: "морков" }, { en: "cucumber", bg: "краставица" }, { en: "pepper", bg: "чушка" }, { en: "salad", bg: "салата" }, { en: "soup", bg: "супа" }, { en: "rice", bg: "ориз" }, { en: "pasta", bg: "паста" }, { en: "sugar", bg: "захар" }, { en: "salt", bg: "сол" }, { en: "oil", bg: "олио" }, { en: "butter", bg: "масло" }, { en: "flour", bg: "брашно" }, { en: "coffee", bg: "кафе" }, { en: "tea", bg: "чай" }, { en: "juice", bg: "сок" }, { en: "wine", bg: "вино" }, { en: "beer", bg: "бира" }, { en: "cake", bg: "торта" }, { en: "biscuit", bg: "бисквита" }, { en: "chocolate", bg: "шоколад" }, { en: "ice cream", bg: "сладолед" }, { en: "breakfast", bg: "закуска" }, { en: "lunch", bg: "обяд" }, { en: "dinner", bg: "вечеря" }, { en: "meal", bg: "хранене" }, { en: "plate", bg: "чиния" }, { en: "cup", bg: "чаша" }, { en: "glass", bg: "чаша" }, { en: "fork", bg: "вилица" }, { en: "knife", bg: "нож" }, { en: "spoon", bg: "лъжица" }, { en: "bottle", bg: "бутилка" }, { en: "kitchen", bg: "кухня" }, { en: "cook", bg: "готвя" }, { en: "bake", bg: "пека" }, { en: "fry", bg: "пържа" }, { en: "boil", bg: "варя" }, { en: "grill", bg: "пека на скара" }, { en: "delicious", bg: "вкусен" }, { en: "tasty", bg: "вкусен" }, { en: "sweet", bg: "сладък" }, { en: "sour", bg: "кисел" }, { en: "bitter", bg: "горчив" }, { en: "spicy", bg: "пикантен" }, { en: "fresh", bg: "пресен" }, { en: "hungry", bg: "гладен" }, { en: "thirsty", bg: "жаден" }, { en: "dessert", bg: "десерт" }, { en: "appetizer", bg: "предястие" }, { en: "main course", bg: "основно ястие" }, { en: "bill", bg: "сметка" }, { en: "menu", bg: "меню" }, { en: "waiter", bg: "сервитьор" }, { en: "waitress", bg: "сервитьорка" }, { en: "chef", bg: "готвач" }, { en: "recipe", bg: "рецепта" }, { en: "ingredient", bg: "съставка" }
        ];
        const wordSet_TravelDirections = [ /* Approx 70 words */
            { en: "left", bg: "ляво" }, { en: "right", bg: "дясно" }, { en: "straight", bg: "направо" }, { en: "forward", bg: "напред" }, { en: "backward", bg: "назад" }, { en: "up", bg: "нагоре" }, { en: "down", bg: "надолу" }, { en: "north", bg: "север" }, { en: "south", bg: "юг" }, { en: "east", bg: "изток" }, { en: "west", bg: "запад" }, { en: "here", bg: "тук" }, { en: "there", bg: "там" }, { en: "near", bg: "близо" }, { en: "far", bg: "далеч" }, { en: "map", bg: "карта" }, { en: "ticket", bg: "билет" }, { en: "passport", bg: "паспорт" }, { en: "luggage", bg: "багаж" }, { en: "suitcase", bg: "куфар" }, { en: "train station", bg: "гара" }, { en: "bus station", bg: "автогара" }, { en: "airport", bg: "летище" }, { en: "port", bg: "пристанище" }, { en: "platform", bg: "перон" }, { en: "gate", bg: "изход" }, { en: "flight", bg: "полет" }, { en: "train", bg: "влак" }, { en: "bus", bg: "автобус" }, { en: "taxi", bg: "такси" }, { en: "ship", bg: "кораб" }, { en: "boat", bg: "лодка" }, { en: "bicycle", bg: "колело" }, { en: "motorcycle", bg: "мотоциклет" }, { en: "subway", bg: "метро" }, { en: "tram", bg: "трамвай" }, { en: "hotel", bg: "хотел" }, { en: "reservation", bg: "резервация" }, { en: "room", bg: "стая" }, { en: "key", bg: "ключ" }, { en: "reception", bg: "рецепция" }, { en: "tourist", bg: "турист" }, { en: "guide", bg: "екскурзовод" }, { en: "souvenir", bg: "сувенир" }, { en: "currency", bg: "валута" }, { en: "exchange", bg: "обмяна" }, { en: "information center", bg: "информационен център" }, { en: "toilet", bg: "тоалетна" }, { en: "entrance", bg: "вход" }, { en: "exit", bg: "изход" }, { en: "open", bg: "отворен" }, { en: "closed", bg: "затворен" }, { en: "journey", bg: "пътуване" }, { en: "trip", bg: "екскурзия" }, { en: "vacation", bg: "ваканция" }, { en: "holiday", bg: "празник" }, { en: "adventure", bg: "приключение" }, { en: "destination", bg: "дестинация" }, { en: "arrival", bg: "пристигане" }, { en: "departure", bg: "заминаване" }, { en: "delay", bg: "закъснение" }, { en: "schedule", bg: "разписание" }, { en: "border", bg: "граница" }, { en: "customs", bg: "митница" }, { en: "visa", bg: "виза" }, { en: "street sign", bg: "пътен знак" }, { en: "traffic light", bg: "светофар" }, { en: "crossroad", bg: "кръстопът" }, { en: "roundabout", bg: "кръгово движение" }, { en: "highway", bg: "магистрала" }, { en: "avenue", bg: "булевард" }, { en: "boulevard", bg: "булевард" }, { en: "address", bg: "адрес" }, { en: "how much", bg: "колко струва" }, { en: "where is", bg: "къде е" }, { en: "I need help", bg: "имам нужда от помощ" }, { en: "emergency", bg: "спешен случай" }, { en: "police", bg: "полиция" }
        ];
        const wordSet_NatureAnimals = [ /* Approx 50-70 words */
            { en: "forest", bg: "гора" }, { en: "mountain", bg: "планина" }, { en: "river", bg: "река" }, { en: "lake", bg: "езеро" }, { en: "sea", bg: "море" }, { en: "ocean", bg: "океан" }, { en: "beach", bg: "плаж" }, { en: "island", bg: "остров" }, { en: "desert", bg: "пустиня" }, { en: "jungle", bg: "джунгла" }, { en: "field", bg: "поле" }, { en: "meadow", bg: "ливада" }, { en: "hill", bg: "хълм" }, { en: "valley", bg: "долина" }, { en: "cave", bg: "пещера" }, { en: "volcano", bg: "вулкан" }, { en: "waterfall", bg: "водопад" }, { en: "sky", bg: "небе" }, { en: "sun", bg: "слънце" }, { en: "moon", bg: "луна" }, { en: "star", bg: "звезда" }, { en: "cloud", bg: "облак" }, { en: "rain", bg: "дъжд" }, { en: "snow", bg: "сняг" }, { en: "wind", bg: "вятър" }, { en: "storm", bg: "буря" }, { en: "fog", bg: "мъгла" }, { en: "ice", bg: "лед" }, { en: "plant", bg: "растение" }, { en: "tree", bg: "дърво" }, { en: "flower", bg: "цвете" }, { en: "grass", bg: "трева" }, { en: "leaf", bg: "листо" }, { en: "root", bg: "корен" }, { en: "seed", bg: "семе" }, { en: "mushroom", bg: "гъба" }, { en: "animal", bg: "животно" }, { en: "dog", bg: "куче" }, { en: "cat", bg: "котка" }, { en: "horse", bg: "кон" }, { en: "cow", bg: "крава" }, { en: "sheep", bg: "овца" }, { en: "pig", bg: "прасе" }, { en: "chicken", bg: "пиле" }, { en: "bird", bg: "птица" }, { en: "fish", bg: "риба" }, { en: "insect", bg: "насекомо" }, { en: "bee", bg: "пчела" }, { en: "butterfly", bg: "пеперуда" }, { en: "spider", bg: "паяк" }, { en: "ant", bg: "мравка" }, { en: "snake", bg: "змия" }, { en: "bear", bg: "мечка" }, { en: "wolf", bg: "вълк" }, { en: "fox", bg: "лисица" }, { en: "lion", bg: "лъв" }, { en: "tiger", bg: "тигър" }, { en: "elephant", bg: "слон" }, { en: "monkey", bg: "маймуна" }, { en: "frog", bg: "жаба" }, { en: "mouse", bg: "мишка" }, { en: "rabbit", bg: "заек" }, { en: "deer", bg: "елен" }, { en: "squirrel", bg: "катерица" }, { en: "eagle", bg: "орел" }, { en: "owl", bg: "бухал" }, { en: "dolphin", bg: "делфин" }, { en: "whale", bg: "кит" }, { en: "shark", bg: "акула" }, { en: "crab", bg: "рак" }
        ];
        const wordSet_HobbiesLeisure = [ /* Approx 50-70 words */
            { en: "reading", bg: "четене" }, { en: "writing", bg: "писане" }, { en: "drawing", bg: "рисуване" }, { en: "painting", bg: "рисуване с бои" }, { en: "singing", bg: "пеене" }, { en: "dancing", bg: "танцуване" }, { en: "music", bg: "музика" }, { en: "instrument", bg: "инструмент" }, { en: "guitar", bg: "китара" }, { en: "piano", bg: "пиано" }, { en: "violin", bg: "цигулка" }, { en: "drum", bg: "барабан" }, { en: "sport", bg: "спорт" }, { en: "football", bg: "футбол" }, { en: "basketball", bg: "баскетбол" }, { en: "tennis", bg: "тенис" }, { en: "volleyball", bg: "волейбол" }, { en: "swimming", bg: "плуване" }, { en: "running", bg: "тичане" }, { en: "cycling", bg: "колоездене" }, { en: "hiking", bg: "туризъм" }, { en: "skiing", bg: "каране на ски" }, { en: "snowboarding", bg: "сноуборд" }, { en: "fishing", bg: "риболов" }, { en: "gardening", bg: "градинарство" }, { en: "cooking", bg: "готвене" }, { en: "baking", bg: "печене" }, { en: "photography", bg: "фотография" }, { en: "camera", bg: "фотоапарат" }, { en: "traveling", bg: "пътуване" }, { en: "camping", bg: "къмпинг" }, { en: "movie", bg: "филм" }, { en: "theater", bg: "театър" }, { en: "concert", bg: "концерт" }, { en: "exhibition", bg: "изложба" }, { en: "museum", bg: "музей" }, { en: "game", bg: "игра" }, { en: "board game", bg: "настолна игра" }, { en: "video game", bg: "видео игра" }, { en: "card game", bg: "игра на карти" }, { en: "puzzle", bg: "пъзел" }, { en: "chess", bg: "шах" }, { en: "collecting", bg: "колекциониране" }, { en: "stamps", bg: "марки" }, { en: "coins", bg: "монети" }, { en: "knitting", bg: "плетене" }, { en: "sewing", bg: "шиене" }, { en: "crafts", bg: "занаяти" }, { en: "yoga", bg: "йога" }, { en: "meditation", bg: "медитация" }, { en: "party", bg: "парти" }, { en: "celebration", bg: "празненство" }, { en: "holiday", bg: "празник" }, { en: "vacation", bg: "ваканция" }, { en: "hobby", bg: "хоби" }, { en: "leisure", bg: "свободно време" }, { en: "fun", bg: "забавление" }, { en: "relaxation", bg: "релаксация" }, { en: "team", bg: "отбор" }, { en: "player", bg: "играч" }, { en: "winner", bg: "победител" }, { en: "loser", bg: "губещ" }, { en: "competition", bg: "състезание" }, { en: "exercise", bg: "упражнение" }, { en: "gym", bg: "фитнес зала" }, { en: "park", bg: "парк" }, { en: "beach", bg: "плаж" }
        ];
        const wordSet_TechnologyInternet = [ /* Approx 50-70 words */
            { en: "computer", bg: "компютър" }, { en: "laptop", bg: "лаптоп" }, { en: "tablet", bg: "таблет" }, { en: "smartphone", bg: "смартфон" }, { en: "phone", bg: "телефон" }, { en: "internet", bg: "интернет" }, { en: "website", bg: "уебсайт" }, { en: "email", bg: "имейл" }, { en: "password", bg: "парола" }, { en: "username", bg: "потребителско име" }, { en: "software", bg: "софтуер" }, { en: "hardware", bg: "хардуер" }, { en: "program", bg: "програма" }, { en: "application", bg: "приложение" }, { en: "file", bg: "файл" }, { en: "folder", bg: "папка" }, { en: "mouse", bg: "мишка" }, { en: "keyboard", bg: "клавиатура" }, { en: "screen", bg: "екран" }, { en: "monitor", bg: "монитор" }, { en: "printer", bg: "принтер" }, { en: "scanner", bg: "скенер" }, { en: "camera", bg: "камера" }, { en: "microphone", bg: "микрофон" }, { en: "speaker", bg: "тонколона" }, { en: "headphones", bg: "слушалки" }, { en: "network", bg: "мрежа" }, { en: "Wi-Fi", bg: "уайфай" }, { en: "bluetooth", bg: "блутут" }, { en: "download", bg: "изтегляне" }, { en: "upload", bg: "качване" }, { en: "link", bg: "връзка" }, { en: "browser", bg: "браузър" }, { en: "search engine", bg: "търсачка" }, { en: "social media", bg: "социални медии" }, { en: "message", bg: "съобщение" }, { en: "chat", bg: "чат" }, { en: "video", bg: "видео" }, { en: "photo", bg: "снимка" }, { en: "online", bg: "онлайн" }, { en: "offline", bg: "офлайн" }, { en: "digital", bg: "дигитален" }, { en: "virtual", bg: "виртуален" }, { en: "robot", bg: "робот" }, { en: "artificial intelligence", bg: "изкуствен интелект" }, { en: "data", bg: "данни" }, { en: "code", bg: "код" }, { en: "developer", bg: "разработчик" }, { en: "programmer", bg: "програмист" }, { en: "server", bg: "сървър" }, { en: "cloud", bg: "облак" }, // (tech context)
            { en: "security", bg: "сигурност" }, { en: "virus", bg: "вирус" }, { en: "antivirus", bg: "антивирусна програма" }, { en: "update", bg: "актуализация" }, { en: "install", bg: "инсталиране" }, { en: "uninstall", bg: "деинсталиране" }, { en: "click", bg: "кликване" }, { en: "scroll", bg: "превъртане" }, { en: "zoom", bg: "увеличаване" }, { en: "settings", bg: "настройки" }, { en: "account", bg: "акаунт" }, { en: "profile", bg: "профил" }, { en: "notification", bg: "известие" }, { en: "device", bg: "устройство" }, { en: "gadget", bg: "джаджа" }, { en: "technology", bg: "технология" }, { en: "innovation", bg: "иновация" }
        ];
        const wordSet_EmotionsFeelings = [ /* Approx 50-70 words */
            { en: "happy", bg: "щастлив" }, { en: "sad", bg: "тъжен" }, { en: "angry", bg: "ядосан" }, { en: "surprised", bg: "изненадан" }, { en: "afraid", bg: "уплашен" }, { en: "scared", bg: "уплашен" }, { en: "excited", bg: "възбуден" }, { en: "nervous", bg: "нервен" }, { en: "anxious", bg: "тревожен" }, { en: "calm", bg: "спокоен" }, { en: "relaxed", bg: "отпуснат" }, { en: "tired", bg: "уморен" }, { en: "bored", bg: "отегчен" }, { en: "confused", bg: "объркан" }, { en: "curious", bg: "любопитен" }, { en: "proud", bg: "горд" }, { en: "ashamed", bg: "засрамен" }, { en: "guilty", bg: "виновен" }, { en: "jealous", bg: "ревнив" }, { en: "envious", bg: "завистлив" }, { en: "hopeful", bg: "надежден" }, { en: "hopeless", bg: "безнадежден" }, { en: "grateful", bg: "благодарен" }, { en: "thankful", bg: "благодарен" }, { en: "lonely", bg: "самотен" }, { en: "loved", bg: "обичан" }, { en: "hated", bg: "мразен" }, { en: "stressed", bg: "стресиран" }, { en: "worried", bg: "притеснен" }, { en: "comfortable", bg: "удобен" }, { en: "uncomfortable", bg: "неудобен" }, { en: "satisfied", bg: "доволен" }, { en: "dissatisfied", bg: "недоволен" }, { en: "disappointed", bg: "разочарован" }, { en: "amused", bg: "забавляван" }, { en: "joyful", bg: "радостен" }, { en: "miserable", bg: "нещастен" }, { en: "content", bg: "доволен" }, { en: "eager", bg: "нетърпелив" }, { en: "enthusiastic", bg: "ентусиазиран" }, { en: "shy", bg: "срамежлив" }, { en: "confident", bg: "уверен" }, { en: "brave", bg: "смел" }, { en: "courageous", bg: "смел" }, { en: "cowardly", bg: "страхлив" }, { en: "optimistic", bg: "оптимистичен" }, { en: "pessimistic", bg: "песимистичен" }, { en: "sympathetic", bg: "състрадателен" }, { en: "empathetic", bg: "съпричастен" }, { en: "patient", bg: "търпелив" }, { en: "impatient", bg: "нетърпелив" }, { en: "mood", bg: "настроение" }, { en: "feeling", bg: "чувство" }, { en: "emotion", bg: "емоция" }, { en: "pleasure", bg: "удоволствие" }, { en: "pain", bg: "болка" }, { en: "fear", bg: "страх" }, { en: "joy", bg: "радост" }, { en: "sorrow", bg: "скръб" }, { en: "anger", bg: "гняв" }, { en: "love", bg: "любов" }, { en: "hate", bg: "омраза" }, { en: "surprise", bg: "изненада" }, { en: "excitement", bg: "вълнение" }, { en: "relief", bg: "облекчение" }, { en: "regret", bg: "съжаление" }, { en: "trust", bg: "доверие" }, { en: "distrust", bg: "недоверие" }
        ];
        const wordSet_AbstractConcepts = [ /* Approx 50-70 words */
            { en: "idea", bg: "идея" }, { en: "concept", bg: "концепция" }, { en: "thought", bg: "мисъл" }, { en: "dream", bg: "мечта" }, { en: "reality", bg: "реалност" }, { en: "truth", bg: "истина" }, { en: "lie", bg: "лъжа" }, { en: "belief", bg: "вяра" }, { en: "faith", bg: "вяра" }, { en: "doubt", bg: "съмнение" }, { en: "knowledge", bg: "знание" }, { en: "wisdom", bg: "мъдрост" }, { en: "ignorance", bg: "невежество" }, { en: "freedom", bg: "свобода" }, { en: "justice", bg: "справедливост" }, { en: "injustice", bg: "несправедливост" }, { en: "equality", bg: "равенство" }, { en: "inequality", bg: "неравенство" }, { en: "power", bg: "сила" }, { en: "authority", bg: "власт" }, { en: "responsibility", bg: "отговорност" }, { en: "duty", bg: "дълг" }, { en: "right", bg: "право" }, // (a right)
            { en: "law", bg: "закон" }, { en: "rule", bg: "правило" }, { en: "society", bg: "общество" }, { en: "culture", bg: "култура" }, { en: "tradition", bg: "традиция" }, { en: "civilization", bg: "цивилизация" }, { en: "progress", bg: "прогрес" }, { en: "change", bg: "промяна" }, { en: "development", bg: "развитие" }, { en: "future", bg: "бъдеще" }, { en: "past", bg: "минало" }, { en: "present", bg: "настояще" }, { en: "moment", bg: "момент" }, { en: "eternity", bg: "вечност" }, { en: "luck", bg: "късмет" }, { en: "fate", bg: "съдба" }, { en: "destiny", bg: "съдба" }, { en: "chance", bg: "шанс" }, { en: "opportunity", bg: "възможност" }, { en: "risk", bg: "риск" }, { en: "success", bg: "успех" }, { en: "failure", bg: "неуспех" }, { en: "goal", bg: "цел" }, { en: "purpose", bg: "цел" }, { en: "meaning", bg: "смисъл" }, { en: "value", bg: "стойност" }, { en: "quality", bg: "качество" }, { en: "quantity", bg: "количество" }, { en: "beauty", bg: "красота" }, { en: "ugliness", bg: "грозота" }, { en: "goodness", bg: "доброта" }, { en: "evil", bg: "зло" }, { en: "virtue", bg: "добродетел" }, { en: "vice", bg: "порок" }, { en: "morality", bg: "морал" }, { en: "ethics", bg: "етика" }, { en: "spirit", bg: "дух" }, { en: "soul", bg: "душа" }, { en: "mind", bg: "ум" }, { en: "consciousness", bg: "съзнание" }, { en: "imagination", bg: "въображение" }, { en: "creativity", bg: "творчество" }, { en: "logic", bg: "логика" }, { en: "reason", bg: "разум" }, { en: "chaos", bg: "хаос" }, { en: "order", bg: "ред" }, { en: "mystery", bg: "мистерия" }, { en: "secret", bg: "тайна" }, { en: "problem", bg: "проблем" }, { en: "solution", bg: "решение" }
        ];

        const commonSentencesList = [ /* Approx 100 sentences */
            { en: "I am learning Bulgarian.", bg: "Аз уча български." }, { en: "This is a big house.", bg: "Това е голяма къща." }, { en: "She reads a book every day.", bg: "Тя чете книга всеки ден." }, { en: "We go to the shop.", bg: "Ние отиваме до магазина." }, { en: "They drink water.", bg: "Те пият вода." }, { en: "My name is Alex.", bg: "Моето име е Алекс." }, { en: "What time is it?", bg: "Колко е часът?" }, { en: "I like this city.", bg: "Харесвам този град." }, { en: "The weather is good today.", bg: "Времето е хубаво днес." }, { en: "Can you help me, please?", bg: "Можеш ли да ми помогнеш, моля?" }, { en: "Where is the car?", bg: "Къде е колата?"}, { en: "I want to eat.", bg: "Искам да ям."}, { en: "This flower is beautiful.", bg: "Това цвете е красиво."}, { en: "He speaks slowly.", bg: "Той говори бавно."}, { en: "The book is on the table.", bg: "Книгата е на масата."}, { en: "Good morning!", bg: "Добро утро!" }, { en: "Good evening!", bg: "Добър вечер!" }, { en: "How are you?", bg: "Как си?" }, { en: "I am fine, thank you.", bg: "Добре съм, благодаря." }, { en: "What is your name?", bg: "Как се казваш?" }, { en: "I don't understand.", bg: "Не разбирам." }, { en: "Excuse me.", bg: "Извинете." }, { en: "See you later.", bg: "До по-късно." }, { en: "Have a nice day!", bg: "Приятен ден!" }, { en: "I live in Sofia.", bg: "Живея в София." }, { en: "He is my friend.", bg: "Той е мой приятел." }, { en: "She has a cat.", bg: "Тя има котка." }, { en: "We like music.", bg: "Ние харесваме музика." }, { en: "They are students.", bg: "Те са ученици." }, { en: "This is my family.", bg: "Това е моето семейство." }, { en: "The sky is blue.", bg: "Небето е синьо." }, { en: "I need some water.", bg: "Трябва ми вода." }, { en: "Let's go to the park.", bg: "Хайде да отидем в парка." }, { en: "This food is delicious.", bg: "Тази храна е вкусна." }, { en: "I am very happy.", bg: "Много съм щастлив." }, { en: "It is cold outside.", bg: "Навън е студено." }, { en: "The shop is open.", bg: "Магазинът е отворен." }, { en: "I want to buy a book.", bg: "Искам да купя книга." }, { en: "He works at the hospital.", bg: "Той работи в болницата." }, { en: "She plays the piano.", bg: "Тя свири на пиано." }, { en: "We watch a movie.", bg: "Ние гледаме филм." }, { en: "They travel a lot.", bg: "Те пътуват много." }, { en: "The train arrives at noon.", bg: "Влакът пристига на обяд." }, { en: "My brother is tall.", bg: "Брат ми е висок." }, { en: "Her sister is young.", bg: "Сестра й е млада." }, { en: "This car is new.", bg: "Тази кола е нова." }, { en: "That house is old.", bg: "Онази къща е стара." }, { en: "The lesson is interesting.", bg: "Урокът е интересен." }, { en: "I speak a little Bulgarian.", bg: "Говоря малко български." }, { en: "The sun is shining.", bg: "Слънцето грее." }, { en: "The dog is playing in the garden.", bg: "Кучето играе в градината." }, { en: "I am reading an interesting story.", bg: "Чета интересна история." }, { en: "She is writing a letter to her friend.", bg: "Тя пише писмо на своя приятелка." }, { en: "What are you doing?", bg: "Какво правиш?" }, { en: "I am going home.", bg: "Отивам си вкъщи." }, { en: "This is very important.", bg: "Това е много важно." }, { en: "I have a question.", bg: "Имам въпрос." }, { en: "Can I ask something?", bg: "Мога ли да попитам нещо?" }, { en: "The restaurant is full.", bg: "Ресторантът е пълен." }, { en: "I would like a coffee.", bg: "Бих искал кафе." }, { en: "The museum is closed on Monday.", bg: "Музеят е затворен в понеделник." }, { en: "My favorite color is green.", bg: "Любимият ми цвят е зелен." }, { en: "He lives in a small village.", bg: "Той живее в малко село." }, { en: "She likes to travel by train.", bg: "Тя обича да пътува с влак." }, { en: "We are going on vacation next week.", bg: "Отиваме на почивка следващата седмица." }, { en: "They bought a new car yesterday.", bg: "Те купиха нова кола вчера." }, { en: "This is my favorite song.", bg: "Това е любимата ми песен." }, { en: "The children are playing outside.", bg: "Децата играят навън." }, { en: "I need to go to the bank.", bg: "Трябва да отида до банката." }, { en: "She is a very good teacher.", bg: "Тя е много добра учителка." }, { en: "He is a famous writer.", bg: "Той е известен писател." }, { en: "We are having dinner at 7 PM.", bg: "Вечеряме в 7 часа." }, { en: "They are watching a football match.", bg: "Те гледат футболен мач." }, { en: "The cat is sleeping on the chair.", bg: "Котката спи на стола." }, { en: "I forgot my keys.", bg: "Забравих си ключовете." }, { en: "It is raining heavily.", bg: "Вали силно." }, { en: "The mountains are beautiful in spring.", bg: "Планините са красиви през пролетта." }, { en: "I want to learn new things.", bg: "Искам да науча нови неща." }, { en: "She always helps her friends.", bg: "Тя винаги помага на приятелите си." }, { en: "He never eats meat.", bg: "Той никога не яде месо." }, { en: "We sometimes go to the cinema.", bg: "Понякога ходим на кино." }, { en: "They often visit their grandparents.", bg: "Те често посещават баба си и дядо си." }, { en: "The library has many books.", bg: "Библиотеката има много книги." }, { en: "I am tired after work.", bg: "Уморен съм след работа." }, { en: "She is happy with her new job.", bg: "Тя е щастлива с новата си работа." }, { en: "He is sad because his team lost.", bg: "Той е тъжен, защото отборът му загуби." }, { en: "This exercise is very difficult.", bg: "Това упражнение е много трудно." }, { en: "The answer is easy.", bg: "Отговорът е лесен." }, { en: "My car is faster than yours.", bg: "Моята кола е по-бърза от твоята." }, { en: "This river is very long.", bg: "Тази река е много дълга." }, { en: "The story was short but interesting.", bg: "Историята беше кратка, но интересна." }, { en: "The building is very high.", bg: "Сградата е много висока." }, { en: "His voice is low.", bg: "Гласът му е нисък." }, { en: "She is a strong woman.", bg: "Тя е силна жена." }, { en: "He felt weak after the illness.", bg: "Той се чувстваше слаб след болестта." }, { en: "The bag is heavy.", bg: "Чантата е тежка." }, { en: "This feather is light.", bg: "Това перо е леко." }, { en: "It is dark in the forest at night.", bg: "Тъмно е в гората през нощта." }, { en: "Please speak more slowly.", bg: "Моля, говорете по-бавно." }, { en: "How much does this cost?", bg: "Колко струва това?" }, { en: "Where is the nearest hotel?", bg: "Къде е най-близкият хотел?" }, { en: "I would like to order food.", bg: "Бих искал да поръчам храна." }, { en: "Do you have a menu in English?", bg: "Имате ли меню на английски?" }, { en: "The bill, please.", bg: "Сметката, моля." }
        ];

        let activeLearningList = []; 
        let currentItemIndex = 0; 
        let playerHP = 3;
        let playerScore = 0;
        let MAX_HP = 3;
        let MAX_HINTS_ALLOWED = 1;
        let hintsUsedThisItem = 0; 
        const SCORE_PER_ITEM = 10;
        
        let selectedDifficulty = 'medium'; 
        let selectedWordOrder = 'random'; 
        let selectedContentType = 'words'; 
        let selectedWordSet = 'core'; 

        // --- DOM Elements ---
        const mainTitleElement = document.getElementById('main-title');
        const gameSetupContainer = document.getElementById('game-setup-container');
        const gameMainContainer = document.getElementById('game-main-container');
        
        const difficultyRadioButtons = document.querySelectorAll('input[name="difficulty"]');
        const wordOrderRadioButtons = document.querySelectorAll('input[name="wordOrder"]');
        const contentTypeRadioButtons = document.querySelectorAll('input[name="contentType"]');
        const wordSetRadioButtons = document.querySelectorAll('input[name="wordSet"]');
        const wordSetSelectionContainer = document.getElementById('word-set-selection-container');
        
        const startGameButton = document.getElementById('start-game-button');
        const exitGameButton = document.getElementById('exit-game-button'); 
        const difficultyFeedbackElement = document.getElementById('difficulty-feedback');

        const hpElement = document.getElementById('player-hp');
        const scoreElement = document.getElementById('player-score');
        const currentFloorElement = document.getElementById('current-floor');
        const totalFloorsElement = document.getElementById('total-floors');
        const progressBarFillElement = document.getElementById('progress-bar-fill');
        const encounterPromptElement = document.getElementById('encounter-prompt');
        const englishWordElement = document.getElementById('english-word'); 
        const translationInputElement = document.getElementById('translation-input');
        const submitButton = document.getElementById('submit-button');
        const hintButton = document.getElementById('hint-button');
        const feedbackMessageElement = document.getElementById('feedback-message');
        const hintTextElement = document.getElementById('hint-text');
        const gameOverContainer = document.getElementById('game-over-container');
        const gameOverMessageElement = document.getElementById('game-over-message');
        const restartButton = document.getElementById('restart-button');

        // --- Game Setup Logic ---
        function updateSelectedOptionStyle(radioGroup) {
            if (!radioGroup || radioGroup.length === 0) return; 
            const groupName = radioGroup[0].name;
            document.querySelectorAll(`input[name="${groupName}"]`).forEach(radio => {
                const label = radio.closest('label');
                if (label) { 
                    if (radio.checked) {
                        label.classList.add('selected-option');
                    } else {
                        label.classList.remove('selected-option');
                    }
                }
            });
        }

        difficultyRadioButtons.forEach(radio => radio.addEventListener('change', (e) => {
            selectedDifficulty = e.target.value;
            updateSelectedOptionStyle(difficultyRadioButtons);
        }));
        wordOrderRadioButtons.forEach(radio => radio.addEventListener('change', (e) => {
            selectedWordOrder = e.target.value;
            updateSelectedOptionStyle(wordOrderRadioButtons);
        }));
        contentTypeRadioButtons.forEach(radio => radio.addEventListener('change', (e) => {
            selectedContentType = e.target.value;
            updateSelectedOptionStyle(contentTypeRadioButtons);
            if (selectedContentType === 'words') {
                wordSetSelectionContainer.classList.remove('hidden');
            } else {
                wordSetSelectionContainer.classList.add('hidden');
            }
        }));
        wordSetRadioButtons.forEach(radio => radio.addEventListener('change', (e) => {
            selectedWordSet = e.target.value;
            updateSelectedOptionStyle(wordSetRadioButtons);
        }));


        startGameButton.addEventListener('click', () => {
            switch (selectedDifficulty) {
                case 'easy': MAX_HP = 3; MAX_HINTS_ALLOWED = 3; break;
                case 'medium': MAX_HP = 2; MAX_HINTS_ALLOWED = 2; break;
                case 'hard': MAX_HP = 1; MAX_HINTS_ALLOWED = 1; break;
                default: MAX_HP = 2; MAX_HINTS_ALLOWED = 2; 
            }
            
            if (selectedContentType === 'words') {
                encounterPromptElement.textContent = "Decipher the ancient script (English Word to Bulgarian):";
                switch(selectedWordSet) {
                    case 'core': activeLearningList = [...wordSet_CoreVocabulary]; break;
                    case 'actions': activeLearningList = [...wordSet_ActionsVerbs]; break;
                    case 'people': activeLearningList = [...wordSet_PeoplePlacesDescriptions]; break;
                    case 'food': activeLearningList = [...wordSet_FoodDrink]; break;
                    case 'travel': activeLearningList = [...wordSet_TravelDirections]; break;
                    case 'nature': activeLearningList = [...wordSet_NatureAnimals]; break;
                    case 'hobbies': activeLearningList = [...wordSet_HobbiesLeisure]; break;
                    case 'tech': activeLearningList = [...wordSet_TechnologyInternet]; break;
                    case 'emotions': activeLearningList = [...wordSet_EmotionsFeelings]; break;
                    case 'concepts': activeLearningList = [...wordSet_AbstractConcepts]; break;
                    default: activeLearningList = [...wordSet_CoreVocabulary];
                }
            } else { 
                activeLearningList = [...commonSentencesList];
                encounterPromptElement.textContent = "Translate the sacred decree (English Sentence to Bulgarian):";
            }

            if (selectedWordOrder === 'random') {
                shuffleArray(activeLearningList);
            }

            gameSetupContainer.classList.add('hidden');
            gameMainContainer.classList.remove('hidden');
            mainTitleElement.classList.add('hidden'); 
            initGame();
        });
        
        exitGameButton.addEventListener('click', () => {
            gameMainContainer.classList.add('hidden');
            gameOverContainer.classList.add('hidden'); 
            gameSetupContainer.classList.remove('hidden');
            mainTitleElement.classList.remove('hidden');
             document.querySelector('input[name="difficulty"][value="medium"]').checked = true;
            document.querySelector('input[name="wordOrder"][value="random"]').checked = true;
            document.querySelector('input[name="contentType"][value="words"]').checked = true;
            document.querySelector('input[name="wordSet"][value="core"]').checked = true; 
            selectedContentType = 'words'; 
            wordSetSelectionContainer.classList.remove('hidden'); 
            updateSelectedOptionStyle(difficultyRadioButtons);
            updateSelectedOptionStyle(wordOrderRadioButtons);
            updateSelectedOptionStyle(contentTypeRadioButtons);
            updateSelectedOptionStyle(wordSetRadioButtons);
        });


        function shuffleArray(array) {
            for (let i = array.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [array[i], array[j]] = [array[j], array[i]];
            }
        }

        function initGame() {
            playerHP = MAX_HP;
            playerScore = 0;
            currentItemIndex = 0;
            totalFloorsElement.textContent = activeLearningList.length; 
            updateStatsUI();
            displayNextItem();
            translationInputElement.value = '';
            translationInputElement.disabled = false;
            submitButton.disabled = false;
            hintButton.disabled = false;
            feedbackMessageElement.textContent = '';
            feedbackMessageElement.className = '';
            hintTextElement.textContent = '';
            gameOverContainer.classList.add('hidden');
            gameMainContainer.classList.remove('hidden');
            gameSetupContainer.classList.add('hidden');
            translationInputElement.focus();
        }

        function updateStatsUI() {
            hpElement.textContent = playerHP;
            scoreElement.textContent = playerScore;
            const floor = Math.min(currentItemIndex + 1, activeLearningList.length);
            currentFloorElement.textContent = floor;
            const progressPercentage = activeLearningList.length > 0 ? (currentItemIndex / activeLearningList.length) * 100 : 0;
            progressBarFillElement.style.width = `${progressPercentage}%`;
            progressBarFillElement.textContent = `${Math.round(progressPercentage)}%`;
            if (currentItemIndex >= activeLearningList.length && activeLearningList.length > 0) {
                 progressBarFillElement.style.width = `100%`;
                 progressBarFillElement.textContent = `100%`;
            }
        }

        function displayNextItem() {
            hintsUsedThisItem = 0; 
            hintButton.disabled = false; 
            hintTextElement.textContent = ''; 
            if (currentItemIndex < activeLearningList.length) {
                const itemPair = activeLearningList[currentItemIndex];
                englishWordElement.textContent = itemPair.en; 
                feedbackMessageElement.textContent = '';
            } else {
                handleGameWin();
            }
            updateStatsUI();
        }

        function checkAnswer() {
            const playerTranslation = translationInputElement.value.trim().toLowerCase();
            const currentItemPair = activeLearningList[currentItemIndex];
            if (!playerTranslation) {
                feedbackMessageElement.textContent = "The runes are blank... Inscribe a translation.";
                feedbackMessageElement.className = 'incorrect';
                return;
            }
            if (playerTranslation === currentItemPair.bg.toLowerCase()) {
                playerScore += SCORE_PER_ITEM;
                feedbackMessageElement.textContent = "Correctly deciphered! The spirits are pleased.";
                feedbackMessageElement.className = 'correct';
                currentItemIndex++;
                if (currentItemIndex < activeLearningList.length) {
                    setTimeout(() => {
                        displayNextItem();
                        translationInputElement.value = '';
                        translationInputElement.focus();
                    }, 1200); 
                } else {
                    handleGameWin();
                }
            } else {
                playerHP--;
                feedbackMessageElement.textContent = `Misinterpreted! The true script for "${currentItemPair.en}" is "${currentItemPair.bg}".`;
                feedbackMessageElement.className = 'incorrect';
                if (playerHP <= 0) {
                    handleGameOver();
                }
            }
            updateStatsUI();
             if (playerHP > 0 && currentItemIndex < activeLearningList.length) {
                translationInputElement.value = ''; 
                translationInputElement.focus();
            }
        }

        function getHint() {
            if (currentItemIndex >= activeLearningList.length || playerHP <= 0 || hintsUsedThisItem >= MAX_HINTS_ALLOWED) {
                hintButton.disabled = true;
                if(hintsUsedThisItem >= MAX_HINTS_ALLOWED) {
                    hintTextElement.textContent = "The spirits offer no more wisdom for this script.";
                }
                return;
            }
            const currentItemPair = activeLearningList[currentItemIndex];
            if (currentItemPair && currentItemPair.bg) {
                hintsUsedThisItem++;
                const charsToRevealMultiplier = selectedContentType === 'sentences' ? 2 : 1;
                const revealedCharsCount = Math.min(hintsUsedThisItem * charsToRevealMultiplier, currentItemPair.bg.length);
                const revealedPart = currentItemPair.bg.substring(0, revealedCharsCount);
                let hiddenPart = "_".repeat(Math.max(0, currentItemPair.bg.length - revealedCharsCount));
                hintTextElement.textContent = `✨ Hint (${hintsUsedThisItem}/${MAX_HINTS_ALLOWED}): ${revealedPart}${hiddenPart}`;
                if (hintsUsedThisItem >= MAX_HINTS_ALLOWED) {
                    hintButton.disabled = true;
                }
            } else {
                hintTextElement.textContent = "The spirits are silent on this one.";
            }
        }

        function handleGameOver() {
            englishWordElement.textContent = 'Path Ended...';
            translationInputElement.disabled = true; submitButton.disabled = true; hintButton.disabled = true;
            gameOverMessageElement.textContent = `Your quest ends here! Final Score: ${playerScore}.`;
            gameOverMessageElement.className = 'game-over-message lose';
            gameOverContainer.classList.remove('hidden');
        }

        function handleGameWin() {
            englishWordElement.textContent = 'Victory!';
            updateStatsUI(); 
            translationInputElement.disabled = true; submitButton.disabled = true; hintButton.disabled = true;
            gameOverMessageElement.textContent = `Master Linguist! You've conquered all scripts! Score: ${playerScore}.`;
            gameOverMessageElement.className = 'game-over-message win';
            gameOverContainer.classList.remove('hidden');
        }

        restartButton.addEventListener('click', () => {
            gameMainContainer.classList.add('hidden');
            gameOverContainer.classList.add('hidden');
            gameSetupContainer.classList.remove('hidden');
            mainTitleElement.classList.remove('hidden');
            document.querySelector('input[name="difficulty"][value="medium"]').checked = true;
            document.querySelector('input[name="wordOrder"][value="random"]').checked = true;
            document.querySelector('input[name="contentType"][value="words"]').checked = true;
            document.querySelector('input[name="wordSet"][value="core"]').checked = true; 
            selectedContentType = 'words'; 
            wordSetSelectionContainer.classList.remove('hidden'); 
            updateSelectedOptionStyle(difficultyRadioButtons);
            updateSelectedOptionStyle(wordOrderRadioButtons);
            updateSelectedOptionStyle(contentTypeRadioButtons);
            updateSelectedOptionStyle(wordSetRadioButtons);
        });
        
        document.addEventListener('DOMContentLoaded', () => {
            gameMainContainer.classList.add('hidden');
            gameSetupContainer.classList.remove('hidden');
            updateSelectedOptionStyle(difficultyRadioButtons);
            updateSelectedOptionStyle(wordOrderRadioButtons);
            updateSelectedOptionStyle(contentTypeRadioButtons);
            updateSelectedOptionStyle(wordSetRadioButtons);
            if (document.querySelector('input[name="contentType"][value="words"]').checked) {
                 wordSetSelectionContainer.classList.remove('hidden');
            } else {
                 wordSetSelectionContainer.classList.add('hidden');
            }
        });

        submitButton.addEventListener('click', checkAnswer);
        translationInputElement.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') { checkAnswer(); }
        });
        hintButton.addEventListener('click', getHint);
    </script>
</body>
</html>
