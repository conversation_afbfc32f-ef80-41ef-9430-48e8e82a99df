from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from .models import SiteSettings, UserProfile

@admin.register(SiteSettings)
class SiteSettingsAdmin(admin.ModelAdmin):
    list_display = ('site_name', 'site_tagline', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('site_name', 'site_tagline', 'site_description')
        }),
        ('Branding & Logo', {
            'fields': ('logo_image', 'background_image'),
            'description': 'Upload images for site branding. Logo should be 200x200px for best results.'
        }),
        ('Hero Section', {
            'fields': ('hero_title_primary', 'hero_title_secondary', 'hero_subtitle'),
            'description': 'Customize the main landing page hero section.'
        }),
        ('Social Links', {
            'fields': ('discord_link', 'github_link'),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        # Only allow one instance
        return not SiteSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of the settings
        return False

class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = 'Profile'
    fields = ('level', 'experience_points', 'total_study_time', 'avatar', 'preferred_language', 'daily_goal')
    readonly_fields = ('level', 'experience_points', 'total_study_time')

class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline,)
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff', 'get_level', 'get_xp')
    list_filter = BaseUserAdmin.list_filter + ('profile__level',)

    def get_level(self, obj):
        return obj.profile.level if hasattr(obj, 'profile') else 'N/A'
    get_level.short_description = 'Level'

    def get_xp(self, obj):
        return obj.profile.experience_points if hasattr(obj, 'profile') else 'N/A'
    get_xp.short_description = 'XP'

# Re-register UserAdmin
admin.site.unregister(User)
admin.site.register(User, UserAdmin)

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'level', 'experience_points', 'preferred_language', 'daily_goal', 'created_at')
    list_filter = ('level', 'preferred_language', 'created_at')
    search_fields = ('user__username', 'user__email', 'preferred_language')
    readonly_fields = ('created_at', 'updated_at', 'get_level_progress')

    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('Gamification', {
            'fields': ('level', 'experience_points', 'total_study_time', 'get_level_progress'),
            'description': 'User progress and achievements'
        }),
        ('Customization', {
            'fields': ('avatar', 'preferred_language', 'daily_goal'),
        }),
        ('Achievements', {
            'fields': ('achievements',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_level_progress(self, obj):
        return f"{obj.get_level_progress():.1f}%"
    get_level_progress.short_description = 'Progress to Next Level'

# Customize admin site header
admin.site.site_header = "Mystic Language Academy Admin"
admin.site.site_title = "MLA Admin"
admin.site.index_title = "Welcome to the Mystic Academy Administration"
