from .models import SiteSettings

def site_settings(request):
    """Make site settings available in all templates"""
    try:
        settings = SiteSettings.objects.first()
        if not settings:
            # Create default settings if none exist
            settings = SiteSettings.objects.create()
        return {'site_settings': settings}
    except Exception:
        # Return default values if database is not ready
        return {
            'site_settings': {
                'site_name': 'Mystic Language Academy',
                'site_tagline': 'Master Languages with Magic',
                'hero_title_primary': 'Mystic',
                'hero_title_secondary': 'Language Academy',
                'hero_subtitle': 'Unlock the ancient secrets of languages through mystical learning paths.',
            }
        }
