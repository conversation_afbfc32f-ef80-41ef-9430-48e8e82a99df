from django.core.management.base import BaseCommand
from core.models import Achievement, Language


class Command(BaseCommand):
    help = 'Create default achievements for the language learning app'

    def handle(self, *args, **options):
        achievements_data = [
            # Roguelike Achievements - Easy
            {
                'name': 'Apprentice Conqueror',
                'description': 'Complete a full roguelike challenge on Easy difficulty',
                'icon': '🧙‍♂️',
                'points': 500,
                'category': 'roguelike'
            },
            # Roguelike Achievements - Medium
            {
                'name': 'Wizard Master',
                'description': 'Complete a full roguelike challenge on Medium difficulty',
                'icon': '🧙‍♀️',
                'points': 1000,
                'category': 'roguelike'
            },
            # Roguelike Achievements - Hard
            {
                'name': 'Archmage Legend',
                'description': 'Complete a full roguelike challenge on Hard difficulty',
                'icon': '🔮',
                'points': 2000,
                'category': 'roguelike'
            },
            # Language-specific achievements
            {
                'name': 'Bulgarian Explorer',
                'description': 'Complete your first Bulgarian roguelike challenge',
                'icon': '🇧🇬',
                'points': 300,
                'category': 'milestone'
            },
            {
                'name': 'Polish Pioneer',
                'description': 'Complete your first Polish roguelike challenge',
                'icon': '🇵🇱',
                'points': 300,
                'category': 'milestone'
            },
            {
                'name': 'German Guardian',
                'description': 'Complete your first German roguelike challenge',
                'icon': '🇩🇪',
                'points': 300,
                'category': 'milestone'
            },
            # Study achievements
            {
                'name': 'First Steps',
                'description': 'Complete your first learning session',
                'icon': '👶',
                'points': 50,
                'category': 'study'
            },
            {
                'name': 'Dedicated Learner',
                'description': 'Study for 7 consecutive days',
                'icon': '📚',
                'points': 200,
                'category': 'streak'
            },
            {
                'name': 'Language Enthusiast',
                'description': 'Reach level 5',
                'icon': '⭐',
                'points': 100,
                'category': 'milestone'
            },
            {
                'name': 'Polyglot',
                'description': 'Complete challenges in all 3 languages',
                'icon': '🌍',
                'points': 1500,
                'category': 'milestone'
            },
            # Perfect scores
            {
                'name': 'Perfect Apprentice',
                'description': 'Complete an Easy roguelike with 100% accuracy',
                'icon': '💯',
                'points': 750,
                'category': 'roguelike'
            },
            {
                'name': 'Perfect Wizard',
                'description': 'Complete a Medium roguelike with 100% accuracy',
                'icon': '🎯',
                'points': 1500,
                'category': 'roguelike'
            },
            {
                'name': 'Perfect Archmage',
                'description': 'Complete a Hard roguelike with 100% accuracy',
                'icon': '👑',
                'points': 3000,
                'category': 'roguelike'
            },
            # Speed achievements
            {
                'name': 'Speed Learner',
                'description': 'Complete a roguelike in under 10 minutes',
                'icon': '⚡',
                'points': 400,
                'category': 'roguelike'
            },
            # Milestone achievements
            {
                'name': 'Century Club',
                'description': 'Learn 100 words',
                'icon': '💯',
                'points': 300,
                'category': 'study'
            },
            {
                'name': 'Sentence Master',
                'description': 'Learn 50 sentences',
                'icon': '📝',
                'points': 250,
                'category': 'study'
            },
        ]

        created_count = 0
        for achievement_data in achievements_data:
            achievement, created = Achievement.objects.get_or_create(
                name=achievement_data['name'],
                defaults=achievement_data
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created achievement: {achievement.icon} {achievement.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Achievement already exists: {achievement.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'\nCreated {created_count} new achievements!')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Total achievements in system: {Achievement.objects.count()}')
        )
