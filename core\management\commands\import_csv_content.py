import csv
import os
from django.core.management.base import BaseCommand
from django.conf import settings
from core.models import Language, WordSet, Word, SentenceSet, Sentence


class Command(BaseCommand):
    help = 'Import language content from CSV files'

    def add_arguments(self, parser):
        parser.add_argument(
            '--language',
            type=str,
            help='Language code (e.g., bg, pl, de)',
            required=True
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing content before importing',
        )

    def handle(self, *args, **options):
        language_code = options['language']
        clear_existing = options['clear']
        
        try:
            language = Language.objects.get(code=language_code)
        except Language.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Language with code "{language_code}" does not exist')
            )
            return

        if clear_existing:
            self.stdout.write('Clearing existing content...')
            Word.objects.filter(word_set__language=language).delete()
            WordSet.objects.filter(language=language).delete()
            Sentence.objects.filter(sentence_set__language=language).delete()
            SentenceSet.objects.filter(language=language).delete()
            self.stdout.write('Existing content cleared.')

        # Define CSV file mappings
        csv_files = {
            'bg': {
                'words': [
                    ('common_words_part1.csv', 'Most Common Words (Part 1)', 'First 100 most common Bulgarian words', 'easy', 1),
                    ('common_words_part2.csv', 'Most Common Words (Part 2)', 'Extended vocabulary and everyday words', 'easy', 2),
                    ('colors_numbers.csv', 'Colors & Numbers', 'Essential colors, numbers, and sizes', 'easy', 3),
                    ('time_weather.csv', 'Time & Weather', 'Time expressions and weather vocabulary', 'medium', 4),
                    ('body_health.csv', 'Body & Health', 'Body parts, health, and medical vocabulary', 'medium', 5),
                    ('food_drinks.csv', 'Food & Drinks', 'Food, drinks, and cooking vocabulary', 'easy', 6),
                    ('family_relationships.csv', 'Family & Relationships', 'Family members and relationship terms', 'easy', 7),
                    ('home_objects.csv', 'Home & Objects', 'Household items, furniture, and everyday objects', 'easy', 8),
                    ('clothes_accessories.csv', 'Clothes & Accessories', 'Clothing, jewelry, and fashion vocabulary', 'medium', 9),
                    ('transportation.csv', 'Transportation', 'Vehicles, travel, and transportation vocabulary', 'medium', 10),
                ],
                'sentences': [
                    ('common_sentences.csv', 'Common Sentences', '100 most useful Bulgarian sentences', 'easy', 1),
                    ('shopping_sentences.csv', 'Shopping Phrases', 'Essential phrases for shopping', 'medium', 2),
                    ('medical_sentences.csv', 'Medical Phrases', 'Essential phrases for medical situations', 'medium', 3),
                    ('work_sentences.csv', 'Work Phrases', 'Essential phrases for work situations', 'medium', 4),
                    ('travel_sentences.csv', 'Travel Phrases', 'Essential phrases for travel and tourism', 'medium', 5),
                ]
            },
            'pl': {
                'words': [
                    ('common_words_part1.csv', 'Most Common Words', '100 most common Polish words', 'easy', 1),
                    ('colors_numbers.csv', 'Colors & Numbers', 'Essential colors, numbers, and sizes', 'easy', 2),
                    ('body_health.csv', 'Body & Health', 'Body parts, health, and medical vocabulary', 'medium', 3),
                    ('home_objects.csv', 'Home & Objects', 'Household items, furniture, and everyday objects', 'easy', 4),
                    ('food_drinks.csv', 'Food & Drinks', 'Food, drinks, and cooking vocabulary', 'easy', 5),
                ],
                'sentences': [
                    ('common_sentences.csv', 'Common Sentences', '100 most useful Polish sentences', 'easy', 1),
                    ('travel_sentences.csv', 'Travel Phrases', 'Essential phrases for travel and tourism', 'medium', 2),
                ]
            },
            'de': {
                'words': [
                    ('common_words_part1.csv', 'Most Common Words', '100 most common German words', 'easy', 1),
                    ('colors_numbers.csv', 'Colors & Numbers', 'Essential colors, numbers, and sizes', 'easy', 2),
                    ('body_health.csv', 'Body & Health', 'Body parts, health, and medical vocabulary', 'medium', 3),
                    ('home_objects.csv', 'Home & Objects', 'Household items, furniture, and everyday objects', 'easy', 4),
                ],
                'sentences': [
                    ('common_sentences.csv', 'Common Sentences', '100 most useful German sentences', 'easy', 1),
                    ('travel_sentences.csv', 'Travel Phrases', 'Essential phrases for travel and tourism', 'medium', 2),
                ]
            }
        }

        if language_code not in csv_files:
            self.stdout.write(
                self.style.ERROR(f'No CSV files defined for language "{language_code}"')
            )
            return

        # Import words
        self.import_words(language, csv_files[language_code]['words'])
        
        # Import sentences
        self.import_sentences(language, csv_files[language_code]['sentences'])

        self.stdout.write(
            self.style.SUCCESS(f'Successfully imported content for {language.name}')
        )

    def import_words(self, language, word_files):
        self.stdout.write(f'Importing words for {language.name}...')
        
        for filename, set_name, description, difficulty, order in word_files:
            csv_path = os.path.join(settings.BASE_DIR, 'data', 'csv', language.code, filename)
            
            if not os.path.exists(csv_path):
                self.stdout.write(
                    self.style.WARNING(f'CSV file not found: {csv_path}')
                )
                continue

            # Create or get word set
            word_set, created = WordSet.objects.get_or_create(
                language=language,
                name=set_name,
                defaults={
                    'description': description,
                    'difficulty': difficulty,
                    'order': order,
                    'is_active': True
                }
            )

            if created:
                self.stdout.write(f'Created word set: {set_name}')
            else:
                self.stdout.write(f'Using existing word set: {set_name}')

            # Import words from CSV
            words_imported = 0
            with open(csv_path, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row_num, row in enumerate(reader, 1):
                    try:
                        word, created = Word.objects.get_or_create(
                            word_set=word_set,
                            english_word=row['english_word'],
                            defaults={
                                'target_word': row['target_word'],
                                'pronunciation': row['pronunciation'],
                                'difficulty': row.get('difficulty', difficulty),
                                'order': row_num,
                                'example_sentence_english': row.get('example_english', ''),
                                'example_sentence_target': row.get('example_target', ''),
                            }
                        )
                        if created:
                            words_imported += 1
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'Error importing word at row {row_num}: {e}')
                        )

            self.stdout.write(f'Imported {words_imported} words from {filename}')

    def import_sentences(self, language, sentence_files):
        self.stdout.write(f'Importing sentences for {language.name}...')
        
        for filename, set_name, description, difficulty, order in sentence_files:
            csv_path = os.path.join(settings.BASE_DIR, 'data', 'csv', language.code, filename)
            
            if not os.path.exists(csv_path):
                self.stdout.write(
                    self.style.WARNING(f'CSV file not found: {csv_path}')
                )
                continue

            # Create or get sentence set
            sentence_set, created = SentenceSet.objects.get_or_create(
                language=language,
                name=set_name,
                defaults={
                    'description': description,
                    'difficulty': difficulty,
                    'order': order,
                    'is_active': True
                }
            )

            if created:
                self.stdout.write(f'Created sentence set: {set_name}')
            else:
                self.stdout.write(f'Using existing sentence set: {set_name}')

            # Import sentences from CSV
            sentences_imported = 0
            with open(csv_path, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row_num, row in enumerate(reader, 1):
                    try:
                        sentence, created = Sentence.objects.get_or_create(
                            sentence_set=sentence_set,
                            english_sentence=row['english_sentence'],
                            defaults={
                                'target_sentence': row['target_sentence'],
                                'pronunciation': row['pronunciation'],
                                'difficulty': row.get('difficulty', difficulty),
                                'order': row_num,
                                'context': row.get('context', ''),
                            }
                        )
                        if created:
                            sentences_imported += 1
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'Error importing sentence at row {row_num}: {e}')
                        )

            self.stdout.write(f'Imported {sentences_imported} sentences from {filename}')
