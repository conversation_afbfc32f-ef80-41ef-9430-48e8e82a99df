# Generated by Django 4.2.7 on 2025-05-31 19:48

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SiteSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_name', models.CharField(default='Mystic Language Academy', max_length=100)),
                ('site_tagline', models.CharField(default='Master Languages with Magic', max_length=200)),
                ('site_description', models.TextField(default='Unlock the ancient secrets of languages through mystical learning paths.')),
                ('logo_image', models.ImageField(blank=True, help_text='Upload a logo image (PNG, JPG, or SVG). Recommended size: 200x200px', null=True, upload_to='site_assets/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['png', 'jpg', 'jpeg', 'svg'])])),
                ('hero_title_primary', models.CharField(default='Mystic', max_length=50)),
                ('hero_title_secondary', models.CharField(default='Language Academy', max_length=50)),
                ('hero_subtitle', models.TextField(default='Unlock the ancient secrets of languages through mystical learning paths. Master tongues with the power of enchanted education.')),
                ('background_image', models.ImageField(blank=True, help_text='Background wallpaper image', null=True, upload_to='site_assets/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['png', 'jpg', 'jpeg'])])),
                ('discord_link', models.URLField(blank=True, help_text='Discord community link')),
                ('github_link', models.URLField(blank=True, help_text='GitHub repository link')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Site Settings',
                'verbose_name_plural': 'Site Settings',
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.PositiveIntegerField(default=1)),
                ('experience_points', models.PositiveIntegerField(default=0)),
                ('total_study_time', models.PositiveIntegerField(default=0, help_text='Total study time in minutes')),
                ('avatar', models.ImageField(blank=True, help_text='Profile avatar image', null=True, upload_to='avatars/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['png', 'jpg', 'jpeg'])])),
                ('preferred_language', models.CharField(blank=True, help_text='Language currently learning', max_length=50)),
                ('daily_goal', models.PositiveIntegerField(default=30, help_text='Daily study goal in minutes')),
                ('achievements', models.JSONField(blank=True, default=list, help_text='List of earned achievements')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Profile',
                'verbose_name_plural': 'User Profiles',
            },
        ),
    ]
