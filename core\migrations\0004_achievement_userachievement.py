# Generated by Django 4.2.7 on 2025-05-31 21:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0003_add_preferred_language_fk'),
    ]

    operations = [
        migrations.CreateModel(
            name='Achievement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField()),
                ('icon', models.CharField(default='🏆', help_text='Emoji icon for the achievement', max_length=10)),
                ('points', models.PositiveIntegerField(default=100, help_text='Points awarded for this achievement')),
                ('category', models.CharField(choices=[('roguelike', 'Roguelike'), ('study', 'Study'), ('streak', 'Streak'), ('milestone', 'Milestone')], default='milestone', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Achievement',
                'verbose_name_plural': 'Achievements',
                'ordering': ['category', 'points'],
            },
        ),
        migrations.CreateModel(
            name='UserAchievement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('difficulty', models.CharField(blank=True, choices=[('easy', 'Easy'), ('medium', 'Medium'), ('hard', 'Hard')], help_text='Difficulty level for this achievement', max_length=20, null=True)),
                ('earned_at', models.DateTimeField(auto_now_add=True)),
                ('achievement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='earned_by', to='core.achievement')),
                ('language', models.ForeignKey(blank=True, help_text='Language-specific achievement', null=True, on_delete=django.db.models.deletion.CASCADE, to='core.language')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='earned_achievements', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Achievement',
                'verbose_name_plural': 'User Achievements',
                'ordering': ['-earned_at'],
                'unique_together': {('user', 'achievement', 'language', 'difficulty')},
            },
        ),
    ]
