from django.db import models
from django.core.validators import FileExtensionValidator

class SiteSettings(models.Model):
    """Site configuration settings manageable through admin panel"""
    site_name = models.CharField(max_length=100, default="Mystic Language Academy")
    site_tagline = models.CharField(max_length=200, default="Master Languages with Magic")
    site_description = models.TextField(default="Unlock the ancient secrets of languages through mystical learning paths.")

    # Logo and branding
    logo_image = models.ImageField(
        upload_to='site_assets/',
        blank=True,
        null=True,
        validators=[FileExtensionValidator(allowed_extensions=['png', 'jpg', 'jpeg', 'svg'])],
        help_text="Upload a logo image (PNG, JPG, or SVG). Recommended size: 200x200px"
    )

    # Hero section customization
    hero_title_primary = models.CharField(max_length=50, default="Mystic")
    hero_title_secondary = models.CharField(max_length=50, default="Language Academy")
    hero_subtitle = models.TextField(default="Unlock the ancient secrets of languages through mystical learning paths. Master tongues with the power of enchanted education.")

    # Background and theme
    background_image = models.ImageField(
        upload_to='site_assets/',
        blank=True,
        null=True,
        validators=[FileExtensionValidator(allowed_extensions=['png', 'jpg', 'jpeg'])],
        help_text="Background wallpaper image"
    )

    # Social links
    discord_link = models.URLField(blank=True, help_text="Discord community link")
    github_link = models.URLField(blank=True, help_text="GitHub repository link")

    # Meta
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Site Settings"
        verbose_name_plural = "Site Settings"

    def __str__(self):
        return f"{self.site_name} Settings"

    def save(self, *args, **kwargs):
        # Ensure only one instance exists
        if not self.pk and SiteSettings.objects.exists():
            raise ValueError("Only one SiteSettings instance is allowed")
        super().save(*args, **kwargs)

class UserProfile(models.Model):
    """Extended user profile for gamification features"""
    user = models.OneToOneField('auth.User', on_delete=models.CASCADE, related_name='profile')

    # Gamification
    level = models.PositiveIntegerField(default=1)
    experience_points = models.PositiveIntegerField(default=0)
    total_study_time = models.PositiveIntegerField(default=0, help_text="Total study time in minutes")

    # Avatar and customization
    avatar = models.ImageField(
        upload_to='avatars/',
        blank=True,
        null=True,
        validators=[FileExtensionValidator(allowed_extensions=['png', 'jpg', 'jpeg'])],
        help_text="Profile avatar image"
    )

    # Preferences
    preferred_language = models.CharField(max_length=50, blank=True, help_text="Language currently learning")
    daily_goal = models.PositiveIntegerField(default=30, help_text="Daily study goal in minutes")

    # Achievements
    achievements = models.JSONField(default=list, blank=True, help_text="List of earned achievements")

    # Meta
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "User Profile"
        verbose_name_plural = "User Profiles"

    def __str__(self):
        return f"{self.user.username}'s Profile"

    def get_level_progress(self):
        """Calculate progress to next level"""
        next_level_xp = self.level * 1000  # 1000 XP per level
        current_level_xp = (self.level - 1) * 1000
        progress = ((self.experience_points - current_level_xp) / (next_level_xp - current_level_xp)) * 100
        return min(max(progress, 0), 100)
