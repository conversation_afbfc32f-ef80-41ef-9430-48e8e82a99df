from django.db import models
from django.core.validators import FileExtensionValidator

class SiteSettings(models.Model):
    """Site configuration settings manageable through admin panel"""
    site_name = models.CharField(max_length=100, default="Mystic Language Academy")
    site_tagline = models.CharField(max_length=200, default="Master Languages with Magic")
    site_description = models.TextField(default="Unlock the ancient secrets of languages through mystical learning paths.")

    # Logo and branding
    logo_image = models.ImageField(
        upload_to='site_assets/',
        blank=True,
        null=True,
        validators=[FileExtensionValidator(allowed_extensions=['png', 'jpg', 'jpeg', 'svg'])],
        help_text="Upload a logo image (PNG, JPG, or SVG). Recommended size: 200x200px"
    )

    # Hero section customization
    hero_title_primary = models.CharField(max_length=50, default="Mystic")
    hero_title_secondary = models.CharField(max_length=50, default="Language Academy")
    hero_subtitle = models.TextField(default="Unlock the ancient secrets of languages through mystical learning paths. Master tongues with the power of enchanted education.")

    # Background and theme
    background_image = models.ImageField(
        upload_to='site_assets/',
        blank=True,
        null=True,
        validators=[FileExtensionValidator(allowed_extensions=['png', 'jpg', 'jpeg'])],
        help_text="Background wallpaper image"
    )

    # Social links
    discord_link = models.URLField(blank=True, help_text="Discord community link")
    github_link = models.URLField(blank=True, help_text="GitHub repository link")

    # Meta
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Site Settings"
        verbose_name_plural = "Site Settings"

    def __str__(self):
        return f"{self.site_name} Settings"

    def save(self, *args, **kwargs):
        # Ensure only one instance exists
        if not self.pk and SiteSettings.objects.exists():
            raise ValueError("Only one SiteSettings instance is allowed")
        super().save(*args, **kwargs)

class UserProfile(models.Model):
    """Extended user profile for gamification features"""
    user = models.OneToOneField('auth.User', on_delete=models.CASCADE, related_name='profile')

    # Gamification
    level = models.PositiveIntegerField(default=1)
    experience_points = models.PositiveIntegerField(default=0)
    total_study_time = models.PositiveIntegerField(default=0, help_text="Total study time in minutes")

    # Avatar and customization
    avatar = models.ImageField(
        upload_to='avatars/',
        blank=True,
        null=True,
        validators=[FileExtensionValidator(allowed_extensions=['png', 'jpg', 'jpeg'])],
        help_text="Profile avatar image"
    )

    # Preferences
    preferred_language_name = models.CharField(max_length=50, blank=True, help_text="Language currently learning")
    preferred_language = models.ForeignKey(
        'Language',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Primary language currently learning"
    )
    daily_goal = models.PositiveIntegerField(default=30, help_text="Daily study goal in minutes")

    # Achievements
    achievements = models.JSONField(default=list, blank=True, help_text="List of earned achievements")

    # Meta
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "User Profile"
        verbose_name_plural = "User Profiles"

    def __str__(self):
        return f"{self.user.username}'s Profile"

    def get_level_progress(self):
        """Calculate progress to next level"""
        next_level_xp = self.level * 1000  # 1000 XP per level
        current_level_xp = (self.level - 1) * 1000
        progress = ((self.experience_points - current_level_xp) / (next_level_xp - current_level_xp)) * 100
        return min(max(progress, 0), 100)

class Language(models.Model):
    """Available languages for learning"""
    name = models.CharField(max_length=50, unique=True, help_text="Language name (e.g., Bulgarian)")
    code = models.CharField(max_length=5, unique=True, help_text="Language code (e.g., bg, pl, de)")
    flag_emoji = models.CharField(max_length=10, blank=True, help_text="Flag emoji for the language")
    description = models.TextField(blank=True, help_text="Description of the language")
    is_active = models.BooleanField(default=True, help_text="Is this language available for learning?")
    difficulty_level = models.CharField(
        max_length=20,
        choices=[
            ('beginner', 'Beginner'),
            ('intermediate', 'Intermediate'),
            ('advanced', 'Advanced'),
        ],
        default='beginner'
    )

    # Meta
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Language"
        verbose_name_plural = "Languages"
        ordering = ['name']

    def __str__(self):
        return f"{self.flag_emoji} {self.name}" if self.flag_emoji else self.name

class WordSet(models.Model):
    """Sets of words for each language"""
    language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='word_sets')
    name = models.CharField(max_length=100, help_text="Name of the word set (e.g., Basic Vocabulary)")
    description = models.TextField(blank=True, help_text="Description of what this set covers")
    difficulty = models.CharField(
        max_length=20,
        choices=[
            ('easy', 'Easy'),
            ('medium', 'Medium'),
            ('hard', 'Hard'),
        ],
        default='easy'
    )
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0, help_text="Display order")

    # Meta
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Word Set"
        verbose_name_plural = "Word Sets"
        ordering = ['language', 'order', 'name']
        unique_together = ['language', 'name']

    def __str__(self):
        return f"{self.language.name} - {self.name}"

    def get_word_count(self):
        return self.words.count()

class Word(models.Model):
    """Individual words in each set"""
    word_set = models.ForeignKey(WordSet, on_delete=models.CASCADE, related_name='words')
    english_word = models.CharField(max_length=200, help_text="English word/phrase")
    target_word = models.CharField(max_length=200, help_text="Translation in target language")
    pronunciation = models.CharField(max_length=300, blank=True, help_text="Pronunciation guide")
    example_sentence_english = models.TextField(blank=True, help_text="Example sentence in English")
    example_sentence_target = models.TextField(blank=True, help_text="Example sentence in target language")
    difficulty = models.CharField(
        max_length=20,
        choices=[
            ('easy', 'Easy'),
            ('medium', 'Medium'),
            ('hard', 'Hard'),
        ],
        default='easy'
    )
    order = models.PositiveIntegerField(default=0, help_text="Order within the set")

    # Meta
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Word"
        verbose_name_plural = "Words"
        ordering = ['word_set', 'order', 'english_word']
        unique_together = ['word_set', 'english_word']

    def __str__(self):
        return f"{self.english_word} → {self.target_word}"

class SentenceSet(models.Model):
    """Sets of sentences for each language"""
    language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='sentence_sets')
    name = models.CharField(max_length=100, help_text="Name of the sentence set (e.g., Daily Conversations)")
    description = models.TextField(blank=True, help_text="Description of what this set covers")
    difficulty = models.CharField(
        max_length=20,
        choices=[
            ('easy', 'Easy'),
            ('medium', 'Medium'),
            ('hard', 'Hard'),
        ],
        default='easy'
    )
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0, help_text="Display order")

    # Meta
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Sentence Set"
        verbose_name_plural = "Sentence Sets"
        ordering = ['language', 'order', 'name']
        unique_together = ['language', 'name']

    def __str__(self):
        return f"{self.language.name} - {self.name}"

    def get_sentence_count(self):
        return self.sentences.count()

class Sentence(models.Model):
    """Individual sentences in each set"""
    sentence_set = models.ForeignKey(SentenceSet, on_delete=models.CASCADE, related_name='sentences')
    english_sentence = models.TextField(help_text="English sentence")
    target_sentence = models.TextField(help_text="Translation in target language")
    pronunciation = models.TextField(blank=True, help_text="Pronunciation guide")
    context = models.TextField(blank=True, help_text="Context or situation where this sentence is used")
    difficulty = models.CharField(
        max_length=20,
        choices=[
            ('easy', 'Easy'),
            ('medium', 'Medium'),
            ('hard', 'Hard'),
        ],
        default='easy'
    )
    order = models.PositiveIntegerField(default=0, help_text="Order within the set")

    # Meta
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Sentence"
        verbose_name_plural = "Sentences"
        ordering = ['sentence_set', 'order', 'english_sentence']
        unique_together = ['sentence_set', 'english_sentence']

    def __str__(self):
        return f"{self.english_sentence[:50]}..."

class UserLanguageProgress(models.Model):
    """Track user progress for each language"""
    user = models.ForeignKey('auth.User', on_delete=models.CASCADE, related_name='language_progress')
    language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='user_progress')

    # Progress tracking
    total_words_learned = models.PositiveIntegerField(default=0)
    total_sentences_learned = models.PositiveIntegerField(default=0)
    current_streak = models.PositiveIntegerField(default=0, help_text="Current daily streak")
    longest_streak = models.PositiveIntegerField(default=0, help_text="Longest daily streak")
    last_study_date = models.DateField(null=True, blank=True)

    # Roguelike game stats
    total_roguelike_games = models.PositiveIntegerField(default=0)
    roguelike_wins = models.PositiveIntegerField(default=0)
    roguelike_losses = models.PositiveIntegerField(default=0)
    highest_roguelike_score = models.PositiveIntegerField(default=0)

    # Meta
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "User Language Progress"
        verbose_name_plural = "User Language Progress"
        unique_together = ['user', 'language']

    def __str__(self):
        return f"{self.user.username} - {self.language.name}"

    def get_win_rate(self):
        if self.total_roguelike_games == 0:
            return 0
        return (self.roguelike_wins / self.total_roguelike_games) * 100

class RoguelikeGameSession(models.Model):
    """Individual roguelike game sessions"""
    user = models.ForeignKey('auth.User', on_delete=models.CASCADE, related_name='roguelike_sessions')
    language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='roguelike_sessions')

    # Game configuration
    game_type = models.CharField(
        max_length=20,
        choices=[
            ('words', 'Words'),
            ('sentences', 'Sentences'),
        ],
        default='words'
    )
    difficulty = models.CharField(
        max_length=20,
        choices=[
            ('easy', 'Easy (3 lives)'),
            ('medium', 'Medium (2 lives)'),
            ('hard', 'Hard (1 life)'),
        ],
        default='easy'
    )
    word_set = models.ForeignKey(WordSet, on_delete=models.CASCADE, null=True, blank=True, related_name='roguelike_sessions')
    sentence_set = models.ForeignKey(SentenceSet, on_delete=models.CASCADE, null=True, blank=True, related_name='roguelike_sessions')
    randomized = models.BooleanField(default=True, help_text="Whether words/sentences are randomized")

    # Game state
    current_progress = models.PositiveIntegerField(default=0, help_text="Current progress (0-100)")
    lives_remaining = models.PositiveIntegerField(default=3)
    score = models.PositiveIntegerField(default=0)
    is_completed = models.BooleanField(default=False)
    is_won = models.BooleanField(default=False)

    # Game data
    questions_data = models.JSONField(default=list, help_text="List of questions for this session")
    answers_data = models.JSONField(default=list, help_text="User answers and correctness")

    # Meta
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Roguelike Game Session"
        verbose_name_plural = "Roguelike Game Sessions"
        ordering = ['-started_at']

    def __str__(self):
        status = "Completed" if self.is_completed else "In Progress"
        return f"{self.user.username} - {self.language.name} {self.game_type.title()} ({status})"

    def get_lives_for_difficulty(self):
        """Get number of lives based on difficulty"""
        lives_map = {'easy': 3, 'medium': 2, 'hard': 1}
        return lives_map.get(self.difficulty, 3)

    def get_accuracy(self):
        """Calculate accuracy percentage"""
        if not self.answers_data:
            return 0
        correct = sum(1 for answer in self.answers_data if answer.get('correct', False))
        total = len(self.answers_data)
        return (correct / total * 100) if total > 0 else 0

class Achievement(models.Model):
    """Available achievements in the system"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    icon = models.CharField(max_length=10, default="🏆", help_text="Emoji icon for the achievement")
    points = models.PositiveIntegerField(default=100, help_text="Points awarded for this achievement")
    category = models.CharField(
        max_length=20,
        choices=[
            ('roguelike', 'Roguelike'),
            ('study', 'Study'),
            ('streak', 'Streak'),
            ('milestone', 'Milestone'),
        ],
        default='milestone'
    )
    is_active = models.BooleanField(default=True)

    # Meta
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Achievement"
        verbose_name_plural = "Achievements"
        ordering = ['category', 'points']

    def __str__(self):
        return f"{self.icon} {self.name}"

class UserAchievement(models.Model):
    """Track which achievements users have earned"""
    user = models.ForeignKey('auth.User', on_delete=models.CASCADE, related_name='earned_achievements')
    achievement = models.ForeignKey(Achievement, on_delete=models.CASCADE, related_name='earned_by')
    language = models.ForeignKey(Language, on_delete=models.CASCADE, null=True, blank=True, help_text="Language-specific achievement")
    difficulty = models.CharField(
        max_length=20,
        choices=[
            ('easy', 'Easy'),
            ('medium', 'Medium'),
            ('hard', 'Hard'),
        ],
        null=True,
        blank=True,
        help_text="Difficulty level for this achievement"
    )

    # Meta
    earned_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "User Achievement"
        verbose_name_plural = "User Achievements"
        unique_together = ['user', 'achievement', 'language', 'difficulty']
        ordering = ['-earned_at']

    def __str__(self):
        return f"{self.user.username} - {self.achievement.name}"
