from django.urls import path
from . import views

app_name = 'core'

urlpatterns = [
    path('', views.landing_page, name='landing'),
    path('profile/', views.profile_view, name='profile'),
    path('select-language/', views.select_language, name='select_language'),

    # Learning modules
    path('progress/', views.progress_view, name='progress'),
    path('study/words/', views.study_words_view, name='study_words'),
    path('study/sentences/', views.study_sentences_view, name='study_sentences'),
    path('flashcards/', views.flashcards_view, name='flashcards'),

    # Games
    path('game/roguelike/', views.roguelike_setup_view, name='roguelike_setup'),
    path('game/quick/', views.quick_game_view, name='quick_game'),

    # API endpoints
    path('api/game-words/', views.get_game_words, name='get_game_words'),
]
