from django.urls import path
from . import views

app_name = 'core'

urlpatterns = [
    path('', views.landing_page, name='landing'),
    path('profile/', views.profile_view, name='profile'),
    path('select-language/', views.select_language, name='select_language'),

    # Learning modules
    path('progress/', views.progress_view, name='progress'),
    path('leaderboard/', views.leaderboard_view, name='leaderboard'),
    path('study/words/', views.study_words_view, name='study_words'),
    path('study/words/<int:set_id>/', views.word_set_detail_view, name='word_set_detail'),
    path('study/sentences/', views.study_sentences_view, name='study_sentences'),
    path('flashcards/', views.flashcards_view, name='flashcards'),
    path('flashcards/practice/', views.flashcard_practice_view, name='flashcard_practice'),

    # Games
    path('game/roguelike/', views.roguelike_setup_view, name='roguelike_setup'),
    path('game/play/', views.roguelike_game_view, name='roguelike_game'),
    path('game/quick/', views.quick_game_view, name='quick_game'),

    # Test pages
    path('test-bulgarian/', views.test_bulgarian_page, name='test_bulgarian'),
    path('working-game/', views.simple_working_game, name='simple_working_game'),

    # API endpoints
    path('api/game-content/', views.get_game_content, name='get_game_content'),
    path('api/complete-game/', views.complete_roguelike_game, name='complete_roguelike_game'),
    path('api/test-game/', views.test_game_api, name='test_game_api'),
    path('api/test-status/', views.test_game_status, name='test_game_status'),
    path('debug/', views.debug_view, name='debug'),
    path('simple-game/', views.simple_game_view, name='simple_game'),
]
