from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from .models import (
    Language, UserLanguageProgress, RoguelikeGameSession,
    WordSet, SentenceSet, Word, Sentence, Achievement, UserAchievement
)

def landing_page(request):
    """Landing page view with dark fantasy theme"""
    return render(request, 'core/landing.html')

@login_required
def profile_view(request):
    """User profile page with language selection and progress"""
    user_profile = request.user.profile
    languages = Language.objects.filter(is_active=True)

    # Get user's language progress
    language_progress = UserLanguageProgress.objects.filter(user=request.user).select_related('language')

    # Get recent roguelike sessions
    recent_sessions = RoguelikeGameSession.objects.filter(
        user=request.user,
        is_completed=True
    ).select_related('language').order_by('-completed_at')[:5]

    # Calculate next level XP
    next_level_xp = (user_profile.level + 1) * 1000

    context = {
        'user_profile': user_profile,
        'languages': languages,
        'language_progress': language_progress,
        'recent_sessions': recent_sessions,
        'next_level_xp': next_level_xp,
    }

    return render(request, 'core/profile.html', context)

@login_required
def select_language(request):
    """AJAX endpoint to select preferred language"""
    if request.method == 'POST':
        language_id = request.POST.get('language_id')
        try:
            language = Language.objects.get(id=language_id, is_active=True)
            request.user.profile.preferred_language = language
            request.user.profile.save()

            # Create or get language progress
            UserLanguageProgress.objects.get_or_create(
                user=request.user,
                language=language
            )

            return JsonResponse({
                'success': True,
                'message': f'Selected {language.name} as your preferred language!'
            })
        except Language.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'Invalid language selected.'
            })

    return JsonResponse({'success': False, 'message': 'Invalid request.'})

@login_required
def progress_view(request):
    """Progress dashboard showing user's learning statistics"""
    user_profile = request.user.profile

    # Get language progress
    language_progress = UserLanguageProgress.objects.filter(
        user=request.user
    ).select_related('language')

    # Get recent sessions
    recent_sessions = RoguelikeGameSession.objects.filter(
        user=request.user,
        is_completed=True
    ).select_related('language').order_by('-completed_at')[:10]

    # Calculate overall stats
    total_words = sum(p.total_words_learned for p in language_progress)
    total_sentences = sum(p.total_sentences_learned for p in language_progress)
    total_games = sum(p.total_roguelike_games for p in language_progress)

    context = {
        'user_profile': user_profile,
        'language_progress': language_progress,
        'recent_sessions': recent_sessions,
        'total_words': total_words,
        'total_sentences': total_sentences,
        'total_games': total_games,
    }

    return render(request, 'core/progress.html', context)

@login_required
def study_words_view(request):
    """Word study interface"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get word sets for the user's preferred language
    word_sets = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).prefetch_related('words').order_by('order', 'name')

    context = {
        'user_profile': user_profile,
        'word_sets': word_sets,
        'language': user_profile.preferred_language,
    }

    return render(request, 'core/study_words.html', context)

@login_required
def word_set_detail_view(request, set_id):
    """Detailed view of a specific word set"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    try:
        word_set = WordSet.objects.get(
            id=set_id,
            language=user_profile.preferred_language,
            is_active=True
        )
        words = Word.objects.filter(word_set=word_set).order_by('order', 'english_word')

        context = {
            'user_profile': user_profile,
            'word_set': word_set,
            'words': words,
            'language': user_profile.preferred_language,
        }

        return render(request, 'core/word_set_detail.html', context)

    except WordSet.DoesNotExist:
        messages.error(request, 'Word set not found.')
        return redirect('core:study_words')

@login_required
def study_sentences_view(request):
    """Sentence study interface"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get sentence sets for the user's preferred language
    sentence_sets = SentenceSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).prefetch_related('sentences').order_by('order', 'name')

    context = {
        'user_profile': user_profile,
        'sentence_sets': sentence_sets,
        'language': user_profile.preferred_language,
    }

    return render(request, 'core/study_sentences.html', context)

@login_required
def flashcards_view(request):
    """Flashcard practice interface"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get all content for flashcards
    word_sets = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).prefetch_related('words')

    sentence_sets = SentenceSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).prefetch_related('sentences')

    context = {
        'user_profile': user_profile,
        'word_sets': word_sets,
        'sentence_sets': sentence_sets,
        'language': user_profile.preferred_language,
    }

    return render(request, 'core/flashcards.html', context)

@login_required
def roguelike_setup_view(request):
    """Roguelike game setup interface"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get available sets
    word_sets = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).order_by('order', 'name')

    sentence_sets = SentenceSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).order_by('order', 'name')

    context = {
        'user_profile': user_profile,
        'word_sets': word_sets,
        'sentence_sets': sentence_sets,
        'language': user_profile.preferred_language,
    }

    return render(request, 'core/roguelike_setup.html', context)

@login_required
def quick_game_view(request):
    """Quick game with default settings"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get the first available word set for quick game
    word_set = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).first()

    if not word_set:
        messages.error(request, 'No word sets available for your language.')
        return redirect('core:profile')

    context = {
        'user_profile': user_profile,
        'content_set': word_set,
        'language': user_profile.preferred_language,
        'difficulty': 'easy',
        'game_type': 'words',
        'set_id': word_set.id,
        'randomize': True,
        'lives': 3,
        'quick_game': True,
    }

    return render(request, 'core/roguelike_game.html', context)

@login_required
def roguelike_game_view(request):
    """Roguelike game with custom configuration"""
    print("=== ROGUELIKE GAME VIEW CALLED ===")
    print(f"User: {request.user}")
    print(f"GET parameters: {dict(request.GET)}")

    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get game configuration from URL parameters
    difficulty = request.GET.get('difficulty', 'easy')
    game_type = request.GET.get('type', 'words')
    set_id = request.GET.get('set', None)
    randomize = request.GET.get('randomize', 'true').lower() == 'true'

    # Validate difficulty
    if difficulty not in ['easy', 'medium', 'hard']:
        difficulty = 'easy'

    # Get the appropriate set
    content_set = None
    if game_type == 'words':
        try:
            content_set = WordSet.objects.get(
                id=set_id,
                language=user_profile.preferred_language
            )
        except (WordSet.DoesNotExist, ValueError, TypeError):
            # Fallback to first available word set
            content_set = WordSet.objects.filter(
                language=user_profile.preferred_language,
                is_active=True
            ).first()
    else:  # sentences
        try:
            content_set = SentenceSet.objects.get(
                id=set_id,
                language=user_profile.preferred_language
            )
        except (SentenceSet.DoesNotExist, ValueError, TypeError):
            # Fallback to first available sentence set
            content_set = SentenceSet.objects.filter(
                language=user_profile.preferred_language,
                is_active=True
            ).first()

    if not content_set:
        messages.error(request, f'No {game_type} sets available for {user_profile.preferred_language.name}.')
        return redirect('core:roguelike_setup')

    # Set lives based on difficulty
    lives_map = {'easy': 3, 'medium': 2, 'hard': 1}
    lives = lives_map.get(difficulty, 3)

    context = {
        'user_profile': user_profile,
        'content_set': content_set,
        'language': user_profile.preferred_language,
        'difficulty': difficulty,
        'game_type': game_type,
        'set_id': content_set.id,
        'randomize': randomize,
        'lives': lives,
        'quick_game': False,
    }

    print(f"=== CONTEXT BEING PASSED ===")
    print(f"set_id: {context['set_id']}")
    print(f"game_type: {context['game_type']}")
    print(f"difficulty: {context['difficulty']}")
    print(f"content_set: {context['content_set']}")
    print(f"randomize: {context['randomize']}")

    return render(request, 'core/roguelike_game.html', context)

def get_game_content(request):
    """API endpoint to get words or sentences for the game - TEMPORARILY NO AUTH FOR TESTING"""
    print(f"=== API CALL DEBUG ===")
    print(f"User: {request.user}")
    print(f"User authenticated: {request.user.is_authenticated}")
    print(f"GET parameters: {dict(request.GET)}")

    set_id = request.GET.get('set') or request.GET.get('set_id')  # Accept both 'set' and 'set_id'
    content_type = request.GET.get('type', 'words')  # 'words' or 'sentences'
    limit = int(request.GET.get('limit', 20))

    print(f"set_id: {set_id}")
    print(f"content_type: {content_type}")
    print(f"limit: {limit}")

    if not set_id:
        print("ERROR: set_id is required")
        return JsonResponse({'error': 'set_id is required'}, status=400)

    try:
        # For testing, don't require authentication - just get the set directly
        if content_type == 'words':
            print(f"Looking for WordSet with id={set_id}")
            try:
                content_set = WordSet.objects.get(id=set_id)
                print(f"Found WordSet: {content_set.name} ({content_set.language.name})")
            except WordSet.DoesNotExist:
                print(f"WordSet {set_id} not found, using first available set")
                content_set = WordSet.objects.first()
                if not content_set:
                    print("ERROR: No word sets available")
                    return JsonResponse({'error': 'No word sets available'}, status=404)
                print(f"Using fallback WordSet: {content_set.name} ({content_set.language.name})")

            items = Word.objects.filter(word_set=content_set).order_by('?')[:limit]
            print(f"Found {items.count()} words")

            items_data = []
            for item in items:
                items_data.append({
                    'english': item.english_word,
                    'target': item.target_word,
                    'pronunciation': item.pronunciation,
                    'example_english': item.example_sentence_english,
                    'example_target': item.example_sentence_target,
                })
            print(f"Prepared {len(items_data)} items")
        else:  # sentences
            print(f"Looking for SentenceSet with id={set_id}")
            try:
                content_set = SentenceSet.objects.get(id=set_id)
                print(f"Found SentenceSet: {content_set.name} ({content_set.language.name})")
            except SentenceSet.DoesNotExist:
                print(f"SentenceSet {set_id} not found, using first available set")
                content_set = SentenceSet.objects.first()
                if not content_set:
                    print("ERROR: No sentence sets available")
                    return JsonResponse({'error': 'No sentence sets available'}, status=404)
                print(f"Using fallback SentenceSet: {content_set.name} ({content_set.language.name})")

            items = Sentence.objects.filter(sentence_set=content_set).order_by('?')[:limit]
            print(f"Found {items.count()} sentences")

            items_data = []
            for item in items:
                items_data.append({
                    'english': item.english_sentence,
                    'target': item.target_sentence,
                    'pronunciation': item.pronunciation,
                    'context': item.context,
                })
            print(f"Prepared {len(items_data)} sentence items")

        response_data = {
            'success': True,
            'items': items_data,
            'set_name': content_set.name,
            'content_type': content_type
        }
        print(f"Returning response: {len(items_data)} items")
        print(f"First item: {items_data[0] if items_data else 'None'}")
        return JsonResponse(response_data)

    except (WordSet.DoesNotExist, SentenceSet.DoesNotExist) as e:
        print(f"ERROR: Set not found - {e}")
        return JsonResponse({'error': f'{content_type.title()} set not found'}, status=404)
    except Exception as e:
        print(f"ERROR: Exception - {e}")
        return JsonResponse({'error': str(e)}, status=500)

def test_game_api(request):
    """Test endpoint to verify game API is working - NO AUTH REQUIRED FOR TESTING"""
    print("=== TEST API CALLED ===")
    print(f"User authenticated: {request.user.is_authenticated}")

    if not request.user.is_authenticated:
        return JsonResponse({'error': 'User not authenticated', 'redirect': '/accounts/login/'})

    user_profile = request.user.profile

    if not user_profile.preferred_language:
        return JsonResponse({'error': 'No preferred language set'})

    # Get first word set
    word_set = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).first()

    if not word_set:
        return JsonResponse({'error': 'No word sets available'})

    # Get word count
    word_count = Word.objects.filter(word_set=word_set).count()

    return JsonResponse({
        'success': True,
        'language': user_profile.preferred_language.name,
        'word_set': word_set.name,
        'word_count': word_count,
        'set_id': word_set.id,
        'user': str(request.user)
    })

@login_required
def flashcard_practice_view(request):
    """Flashcard practice session"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get parameters
    content_type = request.GET.get('type', 'words')  # 'words' or 'sentences'
    set_id = request.GET.get('set', None)

    if not set_id:
        messages.error(request, 'No content set specified.')
        return redirect('core:flashcards')

    # Get the content set
    content_set = None
    if content_type == 'words':
        try:
            content_set = WordSet.objects.get(
                id=set_id,
                language=user_profile.preferred_language
            )
        except WordSet.DoesNotExist:
            messages.error(request, 'Word set not found.')
            return redirect('core:flashcards')
    else:  # sentences
        try:
            content_set = SentenceSet.objects.get(
                id=set_id,
                language=user_profile.preferred_language
            )
        except SentenceSet.DoesNotExist:
            messages.error(request, 'Sentence set not found.')
            return redirect('core:flashcards')

    context = {
        'user_profile': user_profile,
        'content_set': content_set,
        'language': user_profile.preferred_language,
        'content_type': content_type,
        'set_id': set_id,
    }

    return render(request, 'core/flashcard_practice.html', context)

@login_required
def complete_roguelike_game(request):
    """Handle roguelike game completion and award achievements"""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        import json
        data = json.loads(request.body)

        # Get game data
        difficulty = data.get('difficulty', 'easy')
        game_type = data.get('game_type', 'words')
        set_id = data.get('set_id')
        score = data.get('score', 0)
        accuracy = data.get('accuracy', 0)
        completed = data.get('completed', False)
        total_questions = data.get('total_questions', 0)

        user = request.user
        user_profile = user.profile
        language = user_profile.preferred_language

        if not language:
            return JsonResponse({'error': 'No preferred language set'}, status=400)

        # Award points and achievements only if game was completed successfully
        if completed and accuracy >= 50:  # At least 50% accuracy required
            points_awarded = 0
            achievements_earned = []

            # Base points for completion
            difficulty_points = {'easy': 500, 'medium': 1000, 'hard': 2000}
            base_points = difficulty_points.get(difficulty, 500)

            # Bonus points for accuracy
            accuracy_bonus = int((accuracy / 100) * base_points * 0.5)
            total_points = base_points + accuracy_bonus

            # Award points to user
            user_profile.experience_points += total_points
            points_awarded = total_points

            # Check for level up
            leveled_up = user_profile.check_level_up()

            # Award difficulty-based achievement (only once per language+difficulty)
            achievement_names = {
                'easy': 'Apprentice Conqueror',
                'medium': 'Wizard Master',
                'hard': 'Archmage Legend'
            }

            achievement_name = achievement_names.get(difficulty)
            if achievement_name:
                try:
                    achievement = Achievement.objects.get(name=achievement_name)
                    user_achievement, created = UserAchievement.objects.get_or_create(
                        user=user,
                        achievement=achievement,
                        language=language,
                        difficulty=difficulty
                    )
                    if created:
                        achievements_earned.append({
                            'name': achievement.name,
                            'description': achievement.description,
                            'icon': achievement.icon,
                            'points': achievement.points
                        })
                        user_profile.experience_points += achievement.points
                        points_awarded += achievement.points
                except Achievement.DoesNotExist:
                    pass

            # Award language-specific achievement (first completion in this language)
            language_achievements = {
                'bg': 'Bulgarian Explorer',
                'pl': 'Polish Pioneer',
                'de': 'German Guardian'
            }

            lang_achievement_name = language_achievements.get(language.code)
            if lang_achievement_name:
                try:
                    achievement = Achievement.objects.get(name=lang_achievement_name)
                    user_achievement, created = UserAchievement.objects.get_or_create(
                        user=user,
                        achievement=achievement,
                        language=language
                    )
                    if created:
                        achievements_earned.append({
                            'name': achievement.name,
                            'description': achievement.description,
                            'icon': achievement.icon,
                            'points': achievement.points
                        })
                        user_profile.experience_points += achievement.points
                        points_awarded += achievement.points
                except Achievement.DoesNotExist:
                    pass

            # Perfect score achievements
            if accuracy == 100:
                perfect_achievements = {
                    'easy': 'Perfect Apprentice',
                    'medium': 'Perfect Wizard',
                    'hard': 'Perfect Archmage'
                }

                perfect_achievement_name = perfect_achievements.get(difficulty)
                if perfect_achievement_name:
                    try:
                        achievement = Achievement.objects.get(name=perfect_achievement_name)
                        user_achievement, created = UserAchievement.objects.get_or_create(
                            user=user,
                            achievement=achievement,
                            language=language,
                            difficulty=difficulty
                        )
                        if created:
                            achievements_earned.append({
                                'name': achievement.name,
                                'description': achievement.description,
                                'icon': achievement.icon,
                                'points': achievement.points
                            })
                            user_profile.experience_points += achievement.points
                            points_awarded += achievement.points
                    except Achievement.DoesNotExist:
                        pass

            # Save user profile
            user_profile.save()

            # Update language progress
            progress, created = UserLanguageProgress.objects.get_or_create(
                user=user,
                language=language
            )
            progress.total_roguelike_games += 1
            progress.roguelike_wins += 1
            if score > progress.highest_roguelike_score:
                progress.highest_roguelike_score = score
            progress.save()

            return JsonResponse({
                'success': True,
                'points_awarded': points_awarded,
                'achievements_earned': achievements_earned,
                'leveled_up': leveled_up,
                'new_level': user_profile.level,
                'total_points': user_profile.experience_points
            })
        else:
            # Game not completed or low accuracy - no rewards
            return JsonResponse({
                'success': True,
                'points_awarded': 0,
                'achievements_earned': [],
                'leveled_up': False,
                'message': 'Complete the challenge with at least 50% accuracy to earn rewards!'
            })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def leaderboard_view(request):
    """Display global leaderboard"""
    from django.contrib.auth.models import User
    from django.db.models import Count

    # Get top users by experience points
    top_users = User.objects.select_related('profile').filter(
        profile__experience_points__gt=0
    ).order_by('-profile__experience_points')[:50]

    # Get current user's rank
    user_rank = None
    if request.user.profile.experience_points > 0:
        higher_users = User.objects.filter(
            profile__experience_points__gt=request.user.profile.experience_points
        ).count()
        user_rank = higher_users + 1

    # Get achievement statistics
    achievement_stats = UserAchievement.objects.values(
        'achievement__name', 'achievement__icon'
    ).annotate(
        earned_count=Count('id')
    ).order_by('-earned_count')[:10]

    context = {
        'top_users': top_users,
        'user_rank': user_rank,
        'achievement_stats': achievement_stats,
        'total_users': User.objects.filter(profile__experience_points__gt=0).count(),
    }

    return render(request, 'core/leaderboard.html', context)

def debug_view(request):
    """Debug view to test server connectivity"""
    print("=== DEBUG VIEW CALLED ===")
    print(f"User: {request.user}")
    print(f"Authenticated: {request.user.is_authenticated}")
    print(f"Method: {request.method}")
    print(f"Path: {request.path}")

    from django.http import HttpResponse

    html = f"""
    <html>
    <head><title>Debug Page</title></head>
    <body>
        <h1>🔧 Debug Information</h1>
        <p><strong>Server Time:</strong> {request.META.get('HTTP_DATE', 'Unknown')}</p>
        <p><strong>User:</strong> {request.user}</p>
        <p><strong>Authenticated:</strong> {request.user.is_authenticated}</p>
        <p><strong>Session Key:</strong> {request.session.session_key}</p>
        <p><strong>Method:</strong> {request.method}</p>
        <p><strong>Path:</strong> {request.path}</p>

        <h2>🎮 Game Test Links</h2>
        <p><a href="/game/play/?difficulty=easy&type=words&set=33&randomize=true">Test Game</a></p>
        <p><a href="/api/test-game/">Test API</a></p>
        <p><a href="/accounts/login/">Login</a></p>

        <script>
            console.log('Debug page loaded');
            fetch('/api/test-game/')
                .then(response => response.json())
                .then(data => {{
                    console.log('API test result:', data);
                    document.getElementById('api-result').textContent = JSON.stringify(data, null, 2);
                }})
                .catch(error => {{
                    console.error('API test error:', error);
                    document.getElementById('api-result').textContent = 'Error: ' + error.message;
                }});
        </script>

        <h2>📡 API Test Result</h2>
        <pre id="api-result">Loading...</pre>
    </body>
    </html>
    """

    return HttpResponse(html)

@login_required
def simple_game_view(request):
    """Simple working game page"""
    return render(request, 'core/simple_game.html')

def test_game_status(request):
    """Test endpoint to check if the game is working"""
    return JsonResponse({
        'status': 'Game is working!',
        'user': str(request.user),
        'authenticated': request.user.is_authenticated,
        'timestamp': 'now',
        'hardcoded_words': [
            {"english": "window", "target": "прозорец", "pronunciation": "pro-zo-rets"},
            {"english": "what", "target": "какво", "pronunciation": "kak-vo"},
            {"english": "house", "target": "къща", "pronunciation": "kash-ta"}
        ]
    })

def test_bulgarian_page(request):
    """Test page that definitely shows Bulgarian words"""
    print("=== BULGARIAN TEST PAGE CALLED ===")
    print(f"User: {request.user}")
    print(f"Method: {request.method}")
    return render(request, 'core/test_bulgarian.html')

def simple_working_game(request):
    """Simple working game that loads from database"""
    print("=== SIMPLE WORKING GAME CALLED ===")
    print(f"User: {request.user}")
    print(f"Authenticated: {request.user.is_authenticated}")

    # Get parameters
    set_id = request.GET.get('set', '33')
    game_type = request.GET.get('type', 'words')
    difficulty = request.GET.get('difficulty', 'easy')

    print(f"Set ID: {set_id}")
    print(f"Game Type: {game_type}")
    print(f"Difficulty: {difficulty}")

    # Get set name
    set_name = "Unknown Set"
    try:
        if game_type == 'words':
            word_set = WordSet.objects.get(id=set_id)
            set_name = word_set.name
        else:
            sentence_set = SentenceSet.objects.get(id=set_id)
            set_name = sentence_set.name
    except:
        pass

    context = {
        'set_id': set_id,
        'game_type': game_type,
        'difficulty': difficulty,
        'set_name': set_name,
    }

    return render(request, 'core/simple_working_game.html', context)

def test_words_api(request):
    """Simple test API that returns hardcoded Bulgarian words"""
    print("=== TEST WORDS API CALLED ===")
    print(f"GET parameters: {dict(request.GET)}")

    # Return hardcoded Bulgarian words for testing
    test_words = [
        {"english": "hello", "target": "здравей", "pronunciation": "zdra-vey"},
        {"english": "goodbye", "target": "довиждане", "pronunciation": "do-vizh-da-ne"},
        {"english": "thank you", "target": "благодаря", "pronunciation": "bla-go-da-rya"},
        {"english": "yes", "target": "да", "pronunciation": "da"},
        {"english": "no", "target": "не", "pronunciation": "ne"},
        {"english": "water", "target": "вода", "pronunciation": "vo-da"},
        {"english": "food", "target": "храна", "pronunciation": "hra-na"},
        {"english": "house", "target": "къща", "pronunciation": "kash-ta"},
        {"english": "car", "target": "кола", "pronunciation": "ko-la"},
        {"english": "book", "target": "книга", "pronunciation": "kni-ga"},
    ]

    response_data = {
        'success': True,
        'items': test_words,
        'set_name': 'Test Bulgarian Words',
        'total': len(test_words)
    }

    print(f"Returning {len(test_words)} test words")
    return JsonResponse(response_data)
