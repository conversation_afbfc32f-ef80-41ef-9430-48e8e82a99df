from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from .models import (
    Language, UserLanguageProgress, RoguelikeGameSession,
    WordSet, SentenceSet, Word, Sentence
)

def landing_page(request):
    """Landing page view with dark fantasy theme"""
    return render(request, 'core/landing.html')

@login_required
def profile_view(request):
    """User profile page with language selection and progress"""
    user_profile = request.user.profile
    languages = Language.objects.filter(is_active=True)

    # Get user's language progress
    language_progress = UserLanguageProgress.objects.filter(user=request.user).select_related('language')

    # Get recent roguelike sessions
    recent_sessions = RoguelikeGameSession.objects.filter(
        user=request.user,
        is_completed=True
    ).select_related('language').order_by('-completed_at')[:5]

    # Calculate next level XP
    next_level_xp = (user_profile.level + 1) * 1000

    context = {
        'user_profile': user_profile,
        'languages': languages,
        'language_progress': language_progress,
        'recent_sessions': recent_sessions,
        'next_level_xp': next_level_xp,
    }

    return render(request, 'core/profile.html', context)

@login_required
def select_language(request):
    """AJAX endpoint to select preferred language"""
    if request.method == 'POST':
        language_id = request.POST.get('language_id')
        try:
            language = Language.objects.get(id=language_id, is_active=True)
            request.user.profile.preferred_language = language
            request.user.profile.save()

            # Create or get language progress
            UserLanguageProgress.objects.get_or_create(
                user=request.user,
                language=language
            )

            return JsonResponse({
                'success': True,
                'message': f'Selected {language.name} as your preferred language!'
            })
        except Language.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'Invalid language selected.'
            })

    return JsonResponse({'success': False, 'message': 'Invalid request.'})

@login_required
def progress_view(request):
    """Progress dashboard showing user's learning statistics"""
    user_profile = request.user.profile

    # Get language progress
    language_progress = UserLanguageProgress.objects.filter(
        user=request.user
    ).select_related('language')

    # Get recent sessions
    recent_sessions = RoguelikeGameSession.objects.filter(
        user=request.user,
        is_completed=True
    ).select_related('language').order_by('-completed_at')[:10]

    # Calculate overall stats
    total_words = sum(p.total_words_learned for p in language_progress)
    total_sentences = sum(p.total_sentences_learned for p in language_progress)
    total_games = sum(p.total_roguelike_games for p in language_progress)

    context = {
        'user_profile': user_profile,
        'language_progress': language_progress,
        'recent_sessions': recent_sessions,
        'total_words': total_words,
        'total_sentences': total_sentences,
        'total_games': total_games,
    }

    return render(request, 'core/progress.html', context)

@login_required
def study_words_view(request):
    """Word study interface"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get word sets for the user's preferred language
    word_sets = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).prefetch_related('words').order_by('order', 'name')

    context = {
        'user_profile': user_profile,
        'word_sets': word_sets,
        'language': user_profile.preferred_language,
    }

    return render(request, 'core/study_words.html', context)

@login_required
def study_sentences_view(request):
    """Sentence study interface"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get sentence sets for the user's preferred language
    sentence_sets = SentenceSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).prefetch_related('sentences').order_by('order', 'name')

    context = {
        'user_profile': user_profile,
        'sentence_sets': sentence_sets,
        'language': user_profile.preferred_language,
    }

    return render(request, 'core/study_sentences.html', context)

@login_required
def flashcards_view(request):
    """Flashcard practice interface"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get all content for flashcards
    word_sets = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).prefetch_related('words')

    sentence_sets = SentenceSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).prefetch_related('sentences')

    context = {
        'user_profile': user_profile,
        'word_sets': word_sets,
        'sentence_sets': sentence_sets,
        'language': user_profile.preferred_language,
    }

    return render(request, 'core/flashcards.html', context)

@login_required
def roguelike_setup_view(request):
    """Roguelike game setup interface"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get available sets
    word_sets = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).order_by('order', 'name')

    sentence_sets = SentenceSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).order_by('order', 'name')

    context = {
        'user_profile': user_profile,
        'word_sets': word_sets,
        'sentence_sets': sentence_sets,
        'language': user_profile.preferred_language,
    }

    return render(request, 'core/roguelike_setup.html', context)

@login_required
def quick_game_view(request):
    """Quick game with default settings"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get the first available word set for quick game
    word_set = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).first()

    if not word_set:
        messages.error(request, 'No word sets available for your language.')
        return redirect('core:profile')

    context = {
        'user_profile': user_profile,
        'content_set': word_set,
        'language': user_profile.preferred_language,
        'difficulty': 'easy',
        'game_type': 'words',
        'set_id': word_set.id,
        'randomize': True,
        'lives': 3,
        'quick_game': True,
    }

    return render(request, 'core/roguelike_game.html', context)

@login_required
def roguelike_game_view(request):
    """Roguelike game with custom configuration"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get game configuration from URL parameters
    difficulty = request.GET.get('difficulty', 'easy')
    game_type = request.GET.get('type', 'words')
    set_id = request.GET.get('set', None)
    randomize = request.GET.get('randomize', 'true').lower() == 'true'

    # Validate difficulty
    if difficulty not in ['easy', 'medium', 'hard']:
        difficulty = 'easy'

    # Get the appropriate set
    content_set = None
    if game_type == 'words':
        try:
            content_set = WordSet.objects.get(
                id=set_id,
                language=user_profile.preferred_language
            )
        except (WordSet.DoesNotExist, ValueError, TypeError):
            # Fallback to first available word set
            content_set = WordSet.objects.filter(
                language=user_profile.preferred_language,
                is_active=True
            ).first()
    else:  # sentences
        try:
            content_set = SentenceSet.objects.get(
                id=set_id,
                language=user_profile.preferred_language
            )
        except (SentenceSet.DoesNotExist, ValueError, TypeError):
            # Fallback to first available sentence set
            content_set = SentenceSet.objects.filter(
                language=user_profile.preferred_language,
                is_active=True
            ).first()

    if not content_set:
        messages.error(request, f'No {game_type} sets available for {user_profile.preferred_language.name}.')
        return redirect('core:roguelike_setup')

    # Set lives based on difficulty
    lives_map = {'easy': 3, 'medium': 2, 'hard': 1}
    lives = lives_map.get(difficulty, 3)

    context = {
        'user_profile': user_profile,
        'content_set': content_set,
        'language': user_profile.preferred_language,
        'difficulty': difficulty,
        'game_type': game_type,
        'set_id': content_set.id,
        'randomize': randomize,
        'lives': lives,
        'quick_game': False,
    }

    return render(request, 'core/roguelike_game.html', context)

@login_required
def get_game_content(request):
    """API endpoint to get words or sentences for the game"""
    set_id = request.GET.get('set_id')
    content_type = request.GET.get('type', 'words')  # 'words' or 'sentences'
    limit = int(request.GET.get('limit', 20))

    if not set_id:
        return JsonResponse({'error': 'set_id is required'}, status=400)

    try:
        if content_type == 'words':
            content_set = WordSet.objects.get(id=set_id, language=request.user.profile.preferred_language)
            items = Word.objects.filter(word_set=content_set).order_by('?')[:limit]

            items_data = []
            for item in items:
                items_data.append({
                    'english': item.english_word,
                    'target': item.target_word,
                    'pronunciation': item.pronunciation,
                    'example_english': item.example_sentence_english,
                    'example_target': item.example_sentence_target,
                })
        else:  # sentences
            content_set = SentenceSet.objects.get(id=set_id, language=request.user.profile.preferred_language)
            items = Sentence.objects.filter(sentence_set=content_set).order_by('?')[:limit]

            items_data = []
            for item in items:
                items_data.append({
                    'english': item.english_sentence,
                    'target': item.target_sentence,
                    'pronunciation': item.pronunciation,
                    'context': item.context,
                })

        return JsonResponse({
            'success': True,
            'items': items_data,
            'set_name': content_set.name,
            'content_type': content_type
        })

    except (WordSet.DoesNotExist, SentenceSet.DoesNotExist):
        return JsonResponse({'error': f'{content_type.title()} set not found'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def test_game_api(request):
    """Test endpoint to verify game API is working"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        return JsonResponse({'error': 'No preferred language set'})

    # Get first word set
    word_set = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).first()

    if not word_set:
        return JsonResponse({'error': 'No word sets available'})

    # Get word count
    word_count = Word.objects.filter(word_set=word_set).count()

    return JsonResponse({
        'success': True,
        'language': user_profile.preferred_language.name,
        'word_set': word_set.name,
        'word_count': word_count,
        'set_id': word_set.id
    })

@login_required
def flashcard_practice_view(request):
    """Flashcard practice session"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get parameters
    content_type = request.GET.get('type', 'words')  # 'words' or 'sentences'
    set_id = request.GET.get('set', None)

    if not set_id:
        messages.error(request, 'No content set specified.')
        return redirect('core:flashcards')

    # Get the content set
    content_set = None
    if content_type == 'words':
        try:
            content_set = WordSet.objects.get(
                id=set_id,
                language=user_profile.preferred_language
            )
        except WordSet.DoesNotExist:
            messages.error(request, 'Word set not found.')
            return redirect('core:flashcards')
    else:  # sentences
        try:
            content_set = SentenceSet.objects.get(
                id=set_id,
                language=user_profile.preferred_language
            )
        except SentenceSet.DoesNotExist:
            messages.error(request, 'Sentence set not found.')
            return redirect('core:flashcards')

    context = {
        'user_profile': user_profile,
        'content_set': content_set,
        'language': user_profile.preferred_language,
        'content_type': content_type,
        'set_id': set_id,
    }

    return render(request, 'core/flashcard_practice.html', context)
