from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from .models import (
    Language, UserLanguageProgress, RoguelikeGameSession,
    WordSet, SentenceSet, Word, Sentence
)

def landing_page(request):
    """Landing page view with dark fantasy theme"""
    return render(request, 'core/landing.html')

@login_required
def profile_view(request):
    """User profile page with language selection and progress"""
    user_profile = request.user.profile
    languages = Language.objects.filter(is_active=True)

    # Get user's language progress
    language_progress = UserLanguageProgress.objects.filter(user=request.user).select_related('language')

    # Get recent roguelike sessions
    recent_sessions = RoguelikeGameSession.objects.filter(
        user=request.user,
        is_completed=True
    ).select_related('language').order_by('-completed_at')[:5]

    # Calculate next level XP
    next_level_xp = (user_profile.level + 1) * 1000

    context = {
        'user_profile': user_profile,
        'languages': languages,
        'language_progress': language_progress,
        'recent_sessions': recent_sessions,
        'next_level_xp': next_level_xp,
    }

    return render(request, 'core/profile.html', context)

@login_required
def select_language(request):
    """AJAX endpoint to select preferred language"""
    if request.method == 'POST':
        language_id = request.POST.get('language_id')
        try:
            language = Language.objects.get(id=language_id, is_active=True)
            request.user.profile.preferred_language = language
            request.user.profile.save()

            # Create or get language progress
            UserLanguageProgress.objects.get_or_create(
                user=request.user,
                language=language
            )

            return JsonResponse({
                'success': True,
                'message': f'Selected {language.name} as your preferred language!'
            })
        except Language.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'Invalid language selected.'
            })

    return JsonResponse({'success': False, 'message': 'Invalid request.'})

@login_required
def progress_view(request):
    """Progress dashboard showing user's learning statistics"""
    user_profile = request.user.profile

    # Get language progress
    language_progress = UserLanguageProgress.objects.filter(
        user=request.user
    ).select_related('language')

    # Get recent sessions
    recent_sessions = RoguelikeGameSession.objects.filter(
        user=request.user,
        is_completed=True
    ).select_related('language').order_by('-completed_at')[:10]

    # Calculate overall stats
    total_words = sum(p.total_words_learned for p in language_progress)
    total_sentences = sum(p.total_sentences_learned for p in language_progress)
    total_games = sum(p.total_roguelike_games for p in language_progress)

    context = {
        'user_profile': user_profile,
        'language_progress': language_progress,
        'recent_sessions': recent_sessions,
        'total_words': total_words,
        'total_sentences': total_sentences,
        'total_games': total_games,
    }

    return render(request, 'core/progress.html', context)

@login_required
def study_words_view(request):
    """Word study interface"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get word sets for the user's preferred language
    word_sets = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).prefetch_related('words').order_by('order', 'name')

    context = {
        'user_profile': user_profile,
        'word_sets': word_sets,
        'language': user_profile.preferred_language,
    }

    return render(request, 'core/study_words.html', context)

@login_required
def study_sentences_view(request):
    """Sentence study interface"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get sentence sets for the user's preferred language
    sentence_sets = SentenceSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).prefetch_related('sentences').order_by('order', 'name')

    context = {
        'user_profile': user_profile,
        'sentence_sets': sentence_sets,
        'language': user_profile.preferred_language,
    }

    return render(request, 'core/study_sentences.html', context)

@login_required
def flashcards_view(request):
    """Flashcard practice interface"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get all content for flashcards
    word_sets = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).prefetch_related('words')

    sentence_sets = SentenceSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).prefetch_related('sentences')

    context = {
        'user_profile': user_profile,
        'word_sets': word_sets,
        'sentence_sets': sentence_sets,
        'language': user_profile.preferred_language,
    }

    return render(request, 'core/flashcards.html', context)

@login_required
def roguelike_setup_view(request):
    """Roguelike game setup interface"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get available sets
    word_sets = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).order_by('order', 'name')

    sentence_sets = SentenceSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).order_by('order', 'name')

    context = {
        'user_profile': user_profile,
        'word_sets': word_sets,
        'sentence_sets': sentence_sets,
        'language': user_profile.preferred_language,
    }

    return render(request, 'core/roguelike_setup.html', context)

@login_required
def quick_game_view(request):
    """Quick game with default settings"""
    user_profile = request.user.profile

    if not user_profile.preferred_language:
        messages.warning(request, 'Please select a preferred language first.')
        return redirect('core:profile')

    # Get the first available word set for quick game
    word_set = WordSet.objects.filter(
        language=user_profile.preferred_language,
        is_active=True
    ).first()

    if not word_set:
        messages.error(request, 'No word sets available for your language.')
        return redirect('core:profile')

    context = {
        'user_profile': user_profile,
        'word_set': word_set,
        'language': user_profile.preferred_language,
        'quick_game': True,
        'word_set_id': word_set.id,
    }

    return render(request, 'core/roguelike_game.html', context)

@login_required
def get_game_words(request):
    """API endpoint to get words for the game"""
    word_set_id = request.GET.get('word_set_id')
    limit = int(request.GET.get('limit', 20))

    if not word_set_id:
        return JsonResponse({'error': 'word_set_id is required'}, status=400)

    try:
        word_set = WordSet.objects.get(id=word_set_id, language=request.user.profile.preferred_language)
        words = Word.objects.filter(word_set=word_set).order_by('?')[:limit]

        words_data = []
        for word in words:
            words_data.append({
                'english': word.english_word,
                'target': word.target_word,
                'pronunciation': word.pronunciation,
                'example_english': word.example_sentence_english,
                'example_target': word.example_sentence_target,
            })

        return JsonResponse({
            'success': True,
            'words': words_data,
            'word_set_name': word_set.name
        })

    except WordSet.DoesNotExist:
        return JsonResponse({'error': 'Word set not found'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
