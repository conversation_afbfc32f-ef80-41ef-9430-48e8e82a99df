/* Root Variables */
:root {
    --primary-purple: #6a4c93;
    --secondary-purple: #8b5cf6;
    --dark-purple: #4c1d95;
    --gold: #fbbf24;
    --dark-gold: #d97706;
    --bg-dark: #0f0f23;
    --bg-darker: #050510;
    --text-light: #e5e7eb;
    --text-muted: #9ca3af;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--bg-dark);
    color: var(--text-light);
    overflow-x: hidden;
    position: relative;
}

/* Animated Background */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, var(--bg-darker) 0%, var(--bg-dark) 50%, var(--dark-purple) 100%);
}

.stars {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 20s linear infinite;
}

@keyframes sparkle {
    from { transform: translateX(0); }
    to { transform: translateX(-200px); }
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-particles::before,
.floating-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--gold);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
    box-shadow: 0 0 10px var(--gold);
}

.floating-particles::before {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.floating-particles::after {
    top: 60%;
    right: 20%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
}

/* Typography */
.hero-title {
    font-family: 'Cinzel', serif;
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.magic-text {
    background: linear-gradient(45deg, var(--gold), var(--dark-gold));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(251, 191, 36, 0.5);
    animation: glow 2s ease-in-out infinite alternate;
}

.academy-text {
    color: var(--secondary-purple);
    text-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
}

@keyframes glow {
    from { text-shadow: 0 0 20px rgba(251, 191, 36, 0.5); }
    to { text-shadow: 0 0 30px rgba(251, 191, 36, 0.8), 0 0 40px rgba(251, 191, 36, 0.3); }
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-muted);
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Buttons */
.btn-magical {
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border: none;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
    margin-right: 1rem;
}

.btn-magical:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.5);
    background: linear-gradient(45deg, var(--secondary-purple), var(--primary-purple));
}

.btn-mystical {
    border: 2px solid var(--gold);
    color: var(--gold);
    padding: 10px 30px;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    background: transparent;
}

.btn-mystical:hover {
    background: var(--gold);
    color: var(--bg-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(251, 191, 36, 0.3);
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.magical-orb {
    position: relative;
    width: 300px;
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.orb-inner {
    width: 150px;
    height: 150px;
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 3rem;
    color: white;
    box-shadow: 
        0 0 50px rgba(139, 92, 246, 0.6),
        inset 0 0 50px rgba(255, 255, 255, 0.1);
    animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.orb-rings {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.ring {
    position: absolute;
    border: 2px solid;
    border-radius: 50%;
    animation: rotate 10s linear infinite;
}

.ring-1 {
    width: 200px;
    height: 200px;
    top: 50px;
    left: 50px;
    border-color: var(--gold);
    opacity: 0.6;
}

.ring-2 {
    width: 250px;
    height: 250px;
    top: 25px;
    left: 25px;
    border-color: var(--secondary-purple);
    opacity: 0.4;
    animation-direction: reverse;
    animation-duration: 15s;
}

.ring-3 {
    width: 300px;
    height: 300px;
    top: 0;
    left: 0;
    border-color: var(--gold);
    opacity: 0.2;
    animation-duration: 20s;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Sections */
.features-section {
    padding: 100px 0;
    position: relative;
}

.section-title {
    font-family: 'Cinzel', serif;
    font-size: 2.5rem;
    color: var(--gold);
    margin-bottom: 1rem;
}

.section-subtitle {
    color: var(--text-muted);
    font-size: 1.1rem;
}

/* Feature Cards */
.feature-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: var(--gold);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.3);
}

.feature-card h3 {
    color: var(--gold);
    margin-bottom: 1rem;
    font-weight: 600;
}

.feature-card p {
    color: var(--text-muted);
    line-height: 1.6;
}

/* Epic Hooded Wizard Logo */
.wizard-logo {
    position: relative;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    box-shadow:
        0 0 30px rgba(139, 92, 246, 0.6),
        inset 0 0 20px rgba(0, 0, 0, 0.5);
    animation: wizardPulse 4s ease-in-out infinite;
}

.wizard-logo::before {
    content: '';
    position: absolute;
    top: 15%;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 40%;
    background: linear-gradient(180deg, #2d2d4a 0%, #1a1a2e 100%);
    border-radius: 50% 50% 0 0;
    z-index: 1;
}

.wizard-eyes {
    position: absolute;
    top: 35%;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    z-index: 2;
}

.wizard-eye {
    width: 6px;
    height: 6px;
    background: var(--gold);
    border-radius: 50%;
    box-shadow:
        0 0 10px var(--gold),
        0 0 20px var(--gold),
        0 0 30px rgba(251, 191, 36, 0.5);
    animation: eyeGlow 2s ease-in-out infinite alternate;
}

.wizard-hood {
    position: absolute;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    width: 70%;
    height: 50%;
    background: linear-gradient(180deg, #4c1d95 0%, #2d2d4a 50%, transparent 100%);
    border-radius: 50% 50% 20% 20%;
    z-index: 0;
}

@keyframes wizardPulse {
    0%, 100% {
        box-shadow:
            0 0 30px rgba(139, 92, 246, 0.6),
            inset 0 0 20px rgba(0, 0, 0, 0.5);
    }
    50% {
        box-shadow:
            0 0 40px rgba(139, 92, 246, 0.8),
            0 0 60px rgba(251, 191, 36, 0.3),
            inset 0 0 20px rgba(0, 0, 0, 0.5);
    }
}

@keyframes eyeGlow {
    0% {
        box-shadow:
            0 0 10px var(--gold),
            0 0 20px var(--gold),
            0 0 30px rgba(251, 191, 36, 0.5);
    }
    100% {
        box-shadow:
            0 0 15px var(--gold),
            0 0 30px var(--gold),
            0 0 45px rgba(251, 191, 36, 0.8);
    }
}

/* Enhanced magical orb with wizard theme */
.orb-inner {
    position: relative;
}

.orb-inner::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: var(--gold);
    border-radius: 50%;
    box-shadow:
        0 0 20px var(--gold),
        0 0 40px rgba(251, 191, 36, 0.5);
    animation: orbCore 3s ease-in-out infinite;
}

@keyframes orbCore {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.8;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .btn-magical,
    .btn-mystical {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
        margin-right: 0;
    }

    .magical-orb {
        width: 200px;
        height: 200px;
    }

    .orb-inner {
        width: 100px;
        height: 100px;
        font-size: 2rem;
    }

    .wizard-logo {
        width: 60px;
        height: 60px;
    }
}
