{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Enter the Academy - {{ site_settings.site_name }}{% endblock %}

{% block extra_css %}
<style>
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.auth-card {
    background: rgba(15, 15, 35, 0.95);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 20px;
    padding: 3rem;
    max-width: 450px;
    width: 100%;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 50px rgba(139, 92, 246, 0.2);
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(251, 191, 36, 0.1), transparent);
    animation: shimmer 3s ease-in-out infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.4);
    animation: float 3s ease-in-out infinite;
}

.auth-title {
    font-family: 'Cinzel', serif;
    font-size: 1.8rem;
    color: var(--gold);
    margin-bottom: 0.5rem;
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
}

.auth-subtitle {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 10px;
    color: var(--text-light);
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--gold);
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
    color: var(--text-light);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-label {
    color: var(--text-light);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.btn-auth {
    width: 100%;
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border: none;
    padding: 12px;
    border-radius: 10px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.btn-auth:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(139, 92, 246, 0.4);
    background: linear-gradient(45deg, var(--secondary-purple), var(--primary-purple));
}

.auth-links {
    text-align: center;
    margin-top: 1.5rem;
}

.auth-links a {
    color: var(--gold);
    text-decoration: none;
    transition: all 0.3s ease;
}

.auth-links a:hover {
    color: var(--dark-gold);
    text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
}

.divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
    color: var(--text-muted);
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.3), transparent);
}

.divider span {
    background: rgba(15, 15, 35, 0.95);
    padding: 0 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <div class="auth-logo">
                {% if site_settings.logo_image %}
                    <img src="{{ site_settings.logo_image.url }}" alt="Logo" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                {% else %}
                    <div class="wizard-logo">
                        <div class="wizard-hood"></div>
                        <div class="wizard-eyes">
                            <div class="wizard-eye"></div>
                            <div class="wizard-eye"></div>
                        </div>
                    </div>
                {% endif %}
            </div>
            <h1 class="auth-title">Enter the Academy</h1>
            <p class="auth-subtitle">Welcome back, aspiring wizard</p>
        </div>

        <form method="post" action="{% url 'account_login' %}">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}

            <div class="form-group">
                <label for="{{ form.login.id_for_label }}" class="form-label">
                    <i class="fas fa-user me-2"></i>Username or Email
                </label>
                <input type="text" name="{{ form.login.name }}" class="form-control" id="{{ form.login.id_for_label }}"
                       value="{{ form.login.value|default:'' }}" placeholder="Enter username or email" required>
            </div>

            <div class="form-group">
                <label for="{{ form.password.id_for_label }}" class="form-label">
                    <i class="fas fa-lock me-2"></i>Password
                </label>
                <input type="password" name="{{ form.password.name }}" class="form-control" id="{{ form.password.id_for_label }}"
                       placeholder="Enter your password" required>
            </div>

            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" name="{{ form.remember.name }}" class="form-check-input" id="{{ form.remember.id_for_label }}">
                    <label class="form-check-label text-light" for="{{ form.remember.id_for_label }}">
                        Remember me
                    </label>
                </div>
            </div>

            <button type="submit" class="btn btn-auth">
                <i class="fas fa-magic me-2"></i>Cast Login Spell
            </button>
        </form>

        <div class="auth-links">
            <a href="{% url 'account_reset_password' %}">
                <i class="fas fa-key me-1"></i>Forgot your magical password?
            </a>
        </div>

        <div class="divider">
            <span>or</span>
        </div>

        <div class="auth-links">
            <p class="text-muted mb-2">New to the academy?</p>
            <a href="{% url 'account_signup' %}" class="btn btn-outline-warning">
                <i class="fas fa-scroll me-2"></i>Begin Your Journey
            </a>
        </div>
    </div>
</div>
{% endblock %}
