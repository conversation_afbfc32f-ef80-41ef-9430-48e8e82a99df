{% extends 'base.html' %}
{% load static %}

{% block title %}Leave the Academy - {{ site_settings.site_name }}{% endblock %}

{% block extra_css %}
<style>
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.auth-card {
    background: rgba(15, 15, 35, 0.95);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 20px;
    padding: 3rem;
    max-width: 450px;
    width: 100%;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 50px rgba(139, 92, 246, 0.2);
    position: relative;
    overflow: hidden;
    text-align: center;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(251, 191, 36, 0.1), transparent);
    animation: shimmer 3s ease-in-out infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.auth-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.4);
    animation: float 3s ease-in-out infinite;
}

.auth-title {
    font-family: 'Cinzel', serif;
    font-size: 1.8rem;
    color: var(--gold);
    margin-bottom: 1rem;
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
}

.auth-subtitle {
    color: var(--text-muted);
    font-size: 1rem;
    margin-bottom: 2rem;
    line-height: 1.5;
}

.btn-auth {
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border: none;
    padding: 12px 30px;
    border-radius: 10px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    margin: 0.5rem;
    min-width: 150px;
}

.btn-auth:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(139, 92, 246, 0.4);
    background: linear-gradient(45deg, var(--secondary-purple), var(--primary-purple));
}

.btn-secondary {
    background: transparent;
    border: 2px solid var(--gold);
    color: var(--gold);
}

.btn-secondary:hover {
    background: var(--gold);
    color: var(--bg-dark);
}
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-logo">
            {% if site_settings.logo_image %}
                <img src="{{ site_settings.logo_image.url }}" alt="Logo" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
            {% else %}
                <i class="fas fa-door-open"></i>
            {% endif %}
        </div>
        
        <h1 class="auth-title">Farewell, Wizard</h1>
        <p class="auth-subtitle">
            Are you sure you want to leave the academy? Your magical progress will be safely stored for your return.
        </p>

        <form method="post" action="{% url 'account_logout' %}" style="display: inline;">
            {% csrf_token %}
            <button type="submit" class="btn btn-auth">
                <i class="fas fa-sign-out-alt me-2"></i>Yes, Leave Academy
            </button>
        </form>
        
        <a href="{% url 'core:landing' %}" class="btn btn-auth btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Stay & Continue
        </a>
    </div>
</div>
{% endblock %}
