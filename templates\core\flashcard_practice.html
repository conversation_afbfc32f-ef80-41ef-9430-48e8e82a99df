{% extends 'base.html' %}
{% load static %}

{% block title %}Flashcard Practice - {{ content_set.name }} - {{ site_settings.site_name }}{% endblock %}

{% block extra_css %}
<style>
.flashcard-practice-container {
    min-height: 100vh;
    padding: 100px 0 50px;
}

.practice-card {
    background: rgba(15, 15, 35, 0.95);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 50px rgba(139, 92, 246, 0.2);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.practice-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(251, 191, 36, 0.1), transparent);
    animation: shimmer 3s ease-in-out infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.flashcard {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(59, 130, 246, 0.1));
    border: 2px solid rgba(139, 92, 246, 0.4);
    border-radius: 15px;
    padding: 3rem;
    text-align: center;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    transform-style: preserve-3d;
}

.flashcard:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(139, 92, 246, 0.3);
}

.flashcard.flipped {
    transform: rotateY(180deg);
}

.flashcard-front, .flashcard-back {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 3rem;
    backface-visibility: hidden;
}

.flashcard-back {
    transform: rotateY(180deg);
}

.flashcard-text {
    font-size: 1.8rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 1rem;
}

.flashcard-pronunciation {
    font-size: 1.2rem;
    color: rgba(251, 191, 36, 0.9);
    font-style: italic;
    margin-bottom: 1rem;
}

.flashcard-example {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 1rem;
}

.practice-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.btn-control {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.8), rgba(59, 130, 246, 0.8));
    border: none;
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.btn-control:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(139, 92, 246, 0.4);
    color: white;
    text-decoration: none;
}

.btn-control.secondary {
    background: linear-gradient(135deg, rgba(75, 85, 99, 0.8), rgba(55, 65, 81, 0.8));
}

.progress-info {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    height: 8px;
    margin: 1rem 0;
    overflow: hidden;
}

.progress-fill {
    background: linear-gradient(90deg, #8b5cf6, #3b82f6);
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.btn-back {
    background: linear-gradient(135deg, rgba(75, 85, 99, 0.8), rgba(55, 65, 81, 0.8));
    border: none;
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    margin-bottom: 2rem;
}

.btn-back:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(75, 85, 99, 0.4);
    color: white;
    text-decoration: none;
}
</style>
{% endblock %}

{% block content %}
<!-- Navigation Bar -->
{% include 'includes/navbar.html' %}

<div class="flashcard-practice-container">
    <div class="container">
        <!-- Back Button -->
        <a href="{% url 'core:flashcards' %}" class="btn-back">
            <i class="fas fa-arrow-left me-2"></i>Back to Flashcards
        </a>

        <!-- Practice Header -->
        <div class="practice-card">
            <div class="text-center">
                <h1 class="text-white mb-3">{{ content_set.name }}</h1>
                <p class="text-muted">{{ language.flag_emoji }} {{ language.name }} - {{ content_type|title }} Practice</p>
                
                <!-- Progress Info -->
                <div class="progress-info">
                    <div id="progress-text">Card 1 of <span id="total-cards">0</span></div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-bar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Flashcard -->
        <div class="practice-card">
            <div class="flashcard" id="flashcard" onclick="flipCard()">
                <div class="flashcard-front" id="card-front">
                    <div class="flashcard-text" id="front-text">Loading...</div>
                    <div class="text-muted">Click to reveal answer</div>
                </div>
                <div class="flashcard-back" id="card-back">
                    <div class="flashcard-text" id="back-text"></div>
                    <div class="flashcard-pronunciation" id="pronunciation"></div>
                    <div class="flashcard-example" id="example"></div>
                </div>
            </div>
        </div>

        <!-- Controls -->
        <div class="practice-card">
            <div class="practice-controls">
                <button class="btn-control secondary" onclick="previousCard()" id="prev-btn">
                    <i class="fas fa-chevron-left me-2"></i>Previous
                </button>
                <button class="btn-control" onclick="nextCard()" id="next-btn">
                    Next<i class="fas fa-chevron-right ms-2"></i>
                </button>
                <button class="btn-control secondary" onclick="shuffleCards()">
                    <i class="fas fa-random me-2"></i>Shuffle
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Practice configuration
const practiceConfig = {
    setId: {{ set_id }},
    contentType: '{{ content_type }}',
};

// Practice state
let practiceState = {
    cards: [],
    currentIndex: 0,
    isFlipped: false
};

// Load cards when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadCards();
});

async function loadCards() {
    try {
        const response = await fetch(`/api/game-content/?set_id=${practiceConfig.setId}&type=${practiceConfig.contentType}&limit=100`);
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.error || 'Failed to load content');
        }
        
        if (!data.items || data.items.length === 0) {
            throw new Error(`No ${practiceConfig.contentType} available`);
        }
        
        practiceState.cards = data.items;
        document.getElementById('total-cards').textContent = practiceState.cards.length;
        
        showCard();
        updateControls();
        
    } catch (error) {
        console.error('Error loading cards:', error);
        document.getElementById('front-text').textContent = 'Error loading content';
    }
}

function showCard() {
    if (practiceState.cards.length === 0) return;
    
    const card = practiceState.cards[practiceState.currentIndex];
    
    // Reset flip state
    practiceState.isFlipped = false;
    document.getElementById('flashcard').classList.remove('flipped');
    
    // Update front (question)
    document.getElementById('front-text').textContent = card.english;
    
    // Update back (answer)
    document.getElementById('back-text').textContent = card.target;
    document.getElementById('pronunciation').textContent = card.pronunciation || '';
    
    // Update example
    const exampleEl = document.getElementById('example');
    if (practiceConfig.contentType === 'words' && card.example_english) {
        exampleEl.textContent = `Example: ${card.example_english}`;
    } else if (practiceConfig.contentType === 'sentences' && card.context) {
        exampleEl.textContent = `Context: ${card.context}`;
    } else {
        exampleEl.textContent = '';
    }
    
    // Update progress
    updateProgress();
}

function flipCard() {
    practiceState.isFlipped = !practiceState.isFlipped;
    const flashcard = document.getElementById('flashcard');
    
    if (practiceState.isFlipped) {
        flashcard.classList.add('flipped');
    } else {
        flashcard.classList.remove('flipped');
    }
}

function nextCard() {
    if (practiceState.currentIndex < practiceState.cards.length - 1) {
        practiceState.currentIndex++;
        showCard();
        updateControls();
    }
}

function previousCard() {
    if (practiceState.currentIndex > 0) {
        practiceState.currentIndex--;
        showCard();
        updateControls();
    }
}

function shuffleCards() {
    practiceState.cards = practiceState.cards.sort(() => Math.random() - 0.5);
    practiceState.currentIndex = 0;
    showCard();
    updateControls();
}

function updateProgress() {
    const progressText = document.getElementById('progress-text');
    const progressBar = document.getElementById('progress-bar');
    
    const current = practiceState.currentIndex + 1;
    const total = practiceState.cards.length;
    const percentage = (current / total) * 100;
    
    progressText.textContent = `Card ${current} of ${total}`;
    progressBar.style.width = `${percentage}%`;
}

function updateControls() {
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    
    prevBtn.disabled = practiceState.currentIndex === 0;
    nextBtn.disabled = practiceState.currentIndex === practiceState.cards.length - 1;
    
    if (practiceState.currentIndex === practiceState.cards.length - 1) {
        nextBtn.innerHTML = '<i class="fas fa-check me-2"></i>Complete';
    } else {
        nextBtn.innerHTML = 'Next<i class="fas fa-chevron-right ms-2"></i>';
    }
}
</script>
{% endblock %}
