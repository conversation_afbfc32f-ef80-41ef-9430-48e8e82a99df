{% extends 'base.html' %}
{% load static %}

{% block title %}Flashcards - {{ language.name }} - {{ site_settings.site_name }}{% endblock %}

{% block extra_css %}
<style>
.flashcard-container {
    min-height: 100vh;
    padding: 100px 0 50px;
}

.flashcard-card {
    background: rgba(15, 15, 35, 0.95);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 50px rgba(139, 92, 246, 0.2);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.flashcard-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(251, 191, 36, 0.1), transparent);
    animation: shimmer 3s ease-in-out infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.section-title {
    font-family: 'Cinzel', serif;
    font-size: 1.8rem;
    color: var(--gold);
    margin-bottom: 1.5rem;
    text-align: center;
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
}

.content-type-selector {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.type-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(139, 92, 246, 0.3);
    color: var(--text-light);
    padding: 12px 24px;
    margin: 0 0.5rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.type-btn.active {
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border-color: var(--secondary-purple);
    color: white;
}

.type-btn:hover {
    border-color: var(--gold);
    transform: translateY(-2px);
}

.set-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.set-card {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(139, 92, 246, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.set-card:hover {
    border-color: var(--gold);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
}

.set-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.set-title {
    font-size: 1.2rem;
    color: var(--text-light);
    font-weight: 600;
}

.difficulty-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-easy {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid #22c55e;
}

.difficulty-medium {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border: 1px solid #fbbf24;
}

.difficulty-hard {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid #ef4444;
}

.set-description {
    color: var(--text-muted);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.set-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-count {
    color: var(--gold);
    font-weight: 600;
}

.btn-practice {
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 0.9rem;
}

.btn-practice:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.4);
    color: white;
    text-decoration: none;
}

.language-header {
    text-align: center;
    margin-bottom: 2rem;
}

.language-flag {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.language-name {
    font-size: 2rem;
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.language-description {
    color: var(--text-muted);
    font-size: 1.1rem;
}

.btn-back {
    background: transparent;
    border: 2px solid var(--gold);
    color: var(--gold);
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 2rem;
}

.btn-back:hover {
    background: var(--gold);
    color: var(--bg-dark);
    text-decoration: none;
    transform: translateY(-2px);
}
</style>
{% endblock %}

{% block content %}
<!-- Navigation Bar -->
{% include 'includes/navbar.html' %}

<div class="flashcard-container">
    <div class="container">
        <!-- Back Button -->
        <a href="{% url 'core:profile' %}" class="btn-back">
            <i class="fas fa-arrow-left me-2"></i>Back to Profile
        </a>

        <!-- Language Header -->
        <div class="flashcard-card">
            <div class="language-header">
                <div class="language-flag">{{ language.flag_emoji }}</div>
                <h1 class="language-name">{{ language.name }} Flashcards</h1>
                <p class="language-description">Practice with interactive flashcards</p>
            </div>
        </div>

        <!-- Content Type Selector -->
        <div class="flashcard-card">
            <div class="content-type-selector">
                <button class="type-btn active" onclick="showWords()">
                    <i class="fas fa-book me-2"></i>Words
                </button>
                <button class="type-btn" onclick="showSentences()">
                    <i class="fas fa-comments me-2"></i>Sentences
                </button>
            </div>

            <!-- Word Sets -->
            <div id="word-sets" class="content-section">
                <h2 class="section-title">Word Sets</h2>
                {% if word_sets %}
                    <div class="set-grid">
                        {% for word_set in word_sets %}
                        <div class="set-card" onclick="location.href='{% url 'core:flashcard_practice' %}?type=words&set={{ word_set.id }}'">
                            <div class="set-header">
                                <h3 class="set-title">{{ word_set.name }}</h3>
                                <span class="difficulty-badge difficulty-{{ word_set.difficulty }}">
                                    {{ word_set.difficulty }}
                                </span>
                            </div>

                            {% if word_set.description %}
                            <p class="set-description">{{ word_set.description }}</p>
                            {% endif %}

                            <div class="set-stats">
                                <span class="item-count">
                                    <i class="fas fa-book me-1"></i>
                                    {{ word_set.get_word_count }} words
                                </span>
                                <a href="{% url 'core:flashcard_practice' %}?type=words&set={{ word_set.id }}" class="btn-practice">
                                    <i class="fas fa-play me-1"></i>Practice
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-book-open fa-3x mb-3"></i>
                        <p>No word sets available for flashcard practice.</p>
                    </div>
                {% endif %}
            </div>

            <!-- Sentence Sets -->
            <div id="sentence-sets" class="content-section" style="display: none;">
                <h2 class="section-title">Sentence Sets</h2>
                {% if sentence_sets %}
                    <div class="set-grid">
                        {% for sentence_set in sentence_sets %}
                        <div class="set-card" onclick="location.href='{% url 'core:flashcard_practice' %}?type=sentences&set={{ sentence_set.id }}'">
                            <div class="set-header">
                                <h3 class="set-title">{{ sentence_set.name }}</h3>
                                <span class="difficulty-badge difficulty-{{ sentence_set.difficulty }}">
                                    {{ sentence_set.difficulty }}
                                </span>
                            </div>

                            {% if sentence_set.description %}
                            <p class="set-description">{{ sentence_set.description }}</p>
                            {% endif %}

                            <div class="set-stats">
                                <span class="item-count">
                                    <i class="fas fa-comments me-1"></i>
                                    {{ sentence_set.get_sentence_count }} sentences
                                </span>
                                <a href="{% url 'core:flashcard_practice' %}?type=sentences&set={{ sentence_set.id }}" class="btn-practice">
                                    <i class="fas fa-play me-1"></i>Practice
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-comments fa-3x mb-3"></i>
                        <p>No sentence sets available for flashcard practice.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="flashcard-card">
            <h2 class="section-title">Other Learning Options</h2>
            <div class="text-center">
                <a href="{% url 'core:study_words' %}" class="btn-practice me-3">
                    <i class="fas fa-book me-2"></i>Study Words
                </a>
                <a href="{% url 'core:study_sentences' %}" class="btn-practice me-3">
                    <i class="fas fa-comments me-2"></i>Practice Sentences
                </a>
                <a href="{% url 'core:roguelike_setup' %}" class="btn-practice">
                    <i class="fas fa-gamepad me-2"></i>Play Game
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function showWords() {
    document.getElementById('word-sets').style.display = 'block';
    document.getElementById('sentence-sets').style.display = 'none';
    
    // Update button states
    document.querySelectorAll('.type-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
}

function showSentences() {
    document.getElementById('word-sets').style.display = 'none';
    document.getElementById('sentence-sets').style.display = 'block';
    
    // Update button states
    document.querySelectorAll('.type-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
}
</script>
{% endblock %}
