{% extends 'base.html' %}
{% load static %}

{% block title %}Leaderboard - {{ site_settings.site_name }}{% endblock %}

{% block extra_css %}
<style>
.leaderboard-container {
    min-height: 100vh;
    padding: 100px 0 50px;
}

.leaderboard-card {
    background: rgba(15, 15, 35, 0.95);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 50px rgba(139, 92, 246, 0.2);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.leaderboard-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(251, 191, 36, 0.1), transparent);
    animation: shimmer 3s ease-in-out infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.leaderboard-header {
    text-align: center;
    margin-bottom: 2rem;
}

.leaderboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.leaderboard-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.1rem;
}

.user-rank {
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.2), rgba(245, 158, 11, 0.2));
    border: 2px solid rgba(251, 191, 36, 0.4);
    border-radius: 15px;
    padding: 1rem;
    text-align: center;
    margin-bottom: 2rem;
}

.rank-text {
    color: #fbbf24;
    font-size: 1.2rem;
    font-weight: 600;
}

.leaderboard-table {
    width: 100%;
    border-collapse: collapse;
}

.leaderboard-table th {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(59, 130, 246, 0.3));
    color: white;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid rgba(139, 92, 246, 0.5);
}

.leaderboard-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
}

.leaderboard-table tr:hover {
    background: rgba(139, 92, 246, 0.1);
}

.rank-number {
    font-weight: 700;
    font-size: 1.2rem;
}

.rank-1 { color: #ffd700; }
.rank-2 { color: #c0c0c0; }
.rank-3 { color: #cd7f32; }

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.points-display {
    font-weight: 600;
    color: #fbbf24;
}

.level-badge {
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.achievement-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.achievement-stat {
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
}

.achievement-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.achievement-name {
    color: white;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.achievement-count {
    color: #fbbf24;
    font-size: 0.9rem;
}

.btn-back {
    background: linear-gradient(135deg, rgba(75, 85, 99, 0.8), rgba(55, 65, 81, 0.8));
    border: none;
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    margin-bottom: 2rem;
}

.btn-back:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(75, 85, 99, 0.4);
    color: white;
    text-decoration: none;
}

.current-user {
    background: rgba(251, 191, 36, 0.1);
    border-left: 4px solid #fbbf24;
}
</style>
{% endblock %}

{% block content %}
<!-- Navigation Bar -->
{% include 'includes/navbar.html' %}

<div class="leaderboard-container">
    <div class="container">
        <!-- Back Button -->
        <a href="{% url 'core:profile' %}" class="btn-back">
            <i class="fas fa-arrow-left me-2"></i>Back to Profile
        </a>

        <!-- Header -->
        <div class="leaderboard-card">
            <div class="leaderboard-header">
                <h1 class="leaderboard-title">🏆 Global Leaderboard</h1>
                <p class="leaderboard-subtitle">Top language learning wizards from around the world</p>
            </div>

            {% if user_rank %}
            <div class="user-rank">
                <div class="rank-text">
                    Your Rank: #{{ user_rank }} out of {{ total_users }} players
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Leaderboard Table -->
        <div class="leaderboard-card">
            <h2 class="text-white mb-4">🌟 Top Players</h2>
            
            {% if top_users %}
            <div class="table-responsive">
                <table class="leaderboard-table">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Player</th>
                            <th>Level</th>
                            <th>Points</th>
                            <th>Language</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for player in top_users %}
                        <tr {% if player == user %}class="current-user"{% endif %}>
                            <td>
                                <span class="rank-number rank-{{ forloop.counter }}">
                                    {% if forloop.counter == 1 %}🥇
                                    {% elif forloop.counter == 2 %}🥈
                                    {% elif forloop.counter == 3 %}🥉
                                    {% else %}#{{ forloop.counter }}{% endif %}
                                </span>
                            </td>
                            <td>
                                <div class="user-info">
                                    <div class="user-avatar">
                                        {{ player.username|first|upper }}
                                    </div>
                                    <span>{{ player.username }}</span>
                                    {% if player == user %}<span class="text-warning ms-2">(You)</span>{% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="level-badge">Level {{ player.profile.level }}</span>
                            </td>
                            <td>
                                <span class="points-display">{{ player.profile.experience_points|floatformat:0 }} XP</span>
                            </td>
                            <td>
                                {% if player.profile.preferred_language %}
                                    {{ player.profile.preferred_language.flag_emoji }} {{ player.profile.preferred_language.name }}
                                {% else %}
                                    <span class="text-muted">Not set</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center text-muted">
                <i class="fas fa-trophy fa-3x mb-3"></i>
                <p>No players on the leaderboard yet. Be the first!</p>
            </div>
            {% endif %}
        </div>

        <!-- Achievement Statistics -->
        {% if achievement_stats %}
        <div class="leaderboard-card">
            <h2 class="text-white mb-4">🏅 Popular Achievements</h2>
            <div class="achievement-stats">
                {% for stat in achievement_stats %}
                <div class="achievement-stat">
                    <div class="achievement-icon">{{ stat.achievement__icon }}</div>
                    <div class="achievement-name">{{ stat.achievement__name }}</div>
                    <div class="achievement-count">Earned by {{ stat.earned_count }} player{{ stat.earned_count|pluralize }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
