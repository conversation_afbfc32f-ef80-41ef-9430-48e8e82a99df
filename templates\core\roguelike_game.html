{% extends 'base.html' %}
{% load static %}

{% block title %}Quick Game - {{ language.name }} - {{ site_settings.site_name }}{% endblock %}

{% block extra_css %}
<style>
.game-container {
    min-height: 100vh;
    padding: 100px 0 50px;
}

.game-card {
    background: rgba(15, 15, 35, 0.95);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 50px rgba(139, 92, 246, 0.2);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.game-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(251, 191, 36, 0.03), transparent);
    animation: shimmer 8s ease-in-out infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.section-title {
    font-family: 'Cinzel', serif;
    font-size: 1.8rem;
    color: var(--gold);
    margin-bottom: 1.5rem;
    text-align: center;
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.game-info {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.language-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.language-flag {
    font-size: 1.5rem;
}

.language-name {
    color: var(--text-light);
    font-weight: 600;
}

.game-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gold);
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.lives-display {
    display: flex;
    gap: 0.5rem;
    font-size: 1.5rem;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    height: 20px;
    margin: 1rem 0;
    overflow: hidden;
}

.progress-fill {
    background: linear-gradient(45deg, var(--gold), var(--dark-gold));
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0%;
}

.game-area {
    text-align: center;
    padding: 3rem 2rem;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.question-card {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.question-text {
    font-size: 2rem;
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 1rem;
}

.question-prompt {
    color: var(--text-muted);
    margin-bottom: 2rem;
}

.answer-input {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 10px;
    color: var(--text-light);
    padding: 15px 20px;
    font-size: 1.2rem;
    width: 100%;
    max-width: 400px;
    margin: 0 auto 2rem;
    transition: all 0.3s ease;
}

.answer-input:focus {
    outline: none;
    border-color: var(--gold);
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
}

.btn-submit {
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    margin: 0 0.5rem;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(139, 92, 246, 0.4);
}

.btn-skip {
    background: transparent;
    border: 2px solid var(--text-muted);
    color: var(--text-muted);
    padding: 10px 25px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    margin: 0 0.5rem;
}

.btn-skip:hover {
    border-color: var(--gold);
    color: var(--gold);
}

.btn-continue {
    background: linear-gradient(45deg, #17a2b8, #138496);
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    margin: 0 0.5rem;
}

.btn-continue:hover {
    background: linear-gradient(45deg, #138496, #117a8b);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(23, 162, 184, 0.4);
}

/* Decorative Elements */
.game-decorations {
    position: absolute;
    top: 1rem;
    left: 0;
    right: 0;
    pointer-events: none;
    z-index: 1;
}

.decoration-emoji {
    position: absolute;
    font-size: 1.5rem;
    animation: float 6s ease-in-out infinite;
    opacity: 0.7;
}

.decoration-emoji.left {
    left: 1rem;
    animation-delay: 0s;
}

.decoration-emoji.right {
    right: 1rem;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Hint System */
.hint-section {
    margin: 1rem 0;
    text-align: center;
}

.hint-display {
    background: rgba(251, 191, 36, 0.1);
    border: 2px solid rgba(251, 191, 36, 0.3);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: 600;
    color: #fbbf24;
    letter-spacing: 0.2em;
    font-family: monospace;
}

.btn-hint {
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.8), rgba(245, 158, 11, 0.8));
    border: none;
    color: white;
    padding: 0.6rem 1.2rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-hint:hover {
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.9), rgba(245, 158, 11, 0.9));
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(251, 191, 36, 0.4);
}

.btn-hint:disabled {
    background: rgba(75, 85, 99, 0.5);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.feedback {
    margin-top: 2rem;
    padding: 1rem;
    border-radius: 10px;
    font-weight: 600;
    display: none;
}

.feedback.correct {
    background: rgba(34, 197, 94, 0.2);
    border: 1px solid #22c55e;
    color: #22c55e;
}

.feedback.incorrect {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid #ef4444;
    color: #ef4444;
}

.game-setup-info {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
    text-align: center;
}

.setup-title {
    color: var(--gold);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.setup-details {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.btn-back {
    background: transparent;
    border: 2px solid var(--gold);
    color: var(--gold);
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 2rem;
}

.btn-back:hover {
    background: var(--gold);
    color: var(--bg-dark);
    text-decoration: none;
    transform: translateY(-2px);
}
</style>
{% endblock %}

{% block content %}
<!-- Navigation Bar -->
{% include 'includes/navbar.html' %}

<div class="game-container">
    <div class="container">
        <!-- Back Button -->
        <a href="{% url 'core:roguelike_setup' %}" class="btn-back">
            <i class="fas fa-arrow-left me-2"></i>Back to Setup
        </a>

        <!-- Game Header -->
        <div class="game-card">
            <div class="game-header">
                <div class="game-info">
                    <div class="language-info">
                        <span class="language-flag">{{ language.flag_emoji }}</span>
                        <span class="language-name">{{ language.name }}</span>
                    </div>
                </div>
                
                <div class="game-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="score">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="progress">0/100</div>
                        <div class="stat-label">Progress</div>
                    </div>
                    <div class="stat-item">
                        <div class="lives-display" id="lives">
                            ❤️❤️❤️
                        </div>
                        <div class="stat-label">Lives</div>
                    </div>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progress-bar"></div>
            </div>
        </div>

        <!-- Game Setup Info -->
        <div class="game-card">
            <div class="game-setup-info">
                {% if quick_game %}
                    <div class="setup-title">Quick Game - {{ content_set.name }}</div>
                    <div class="setup-details">
                        Easy difficulty • {{ content_set.get_word_count }} words available • Randomized order
                    </div>
                {% else %}
                    <div class="setup-title">{{ content_set.name }}</div>
                    <div class="setup-details">
                        {{ difficulty|title }} difficulty •
                        {% if game_type == 'words' %}
                            {{ content_set.get_word_count }} words available
                        {% else %}
                            {{ content_set.get_sentence_count }} sentences available
                        {% endif %}
                        {% if randomize %} • Randomized order{% endif %}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Game Area -->
        <div class="game-card">
            <!-- Decorative Elements -->
            <div class="game-decorations">
                <span class="decoration-emoji left">🌟</span>
                <span class="decoration-emoji right">✨</span>
            </div>

            <div class="game-area">
                <div class="question-card">
                    <div class="question-text" id="question-text">
                        Loading your first challenge...
                    </div>
                    <div class="question-prompt">
                        Translate this word to {{ language.name }}:
                    </div>

                    <!-- Hint Section -->
                    <div class="hint-section" id="hint-section">
                        <div class="hint-display" id="hint-display" style="display: none;"></div>
                        <button class="btn-hint" id="hint-btn" type="button">
                            <i class="fas fa-lightbulb me-1"></i>Get Hint (<span id="hint-count">0</span> left)
                        </button>
                    </div>

                    <input type="text" class="answer-input" id="answer-input" placeholder="Enter your answer..." disabled>
                    
                    <div class="game-buttons">
                        <button class="btn-submit" id="submit-btn" onclick="submitAnswer()" disabled>
                            <i class="fas fa-check me-2"></i>Submit
                        </button>
                        <button class="btn-skip" id="skip-btn" onclick="skipQuestion()" disabled>
                            <i class="fas fa-forward me-2"></i>Skip
                        </button>
                        <button class="btn-continue" id="continue-btn" onclick="continueAfterWrong()" style="display: none;">
                            <i class="fas fa-arrow-right me-2"></i>Continue
                        </button>
                    </div>
                    
                    <div class="feedback" id="feedback"></div>
                </div>
            </div>
        </div>

        <!-- Game Actions -->
        <div class="game-card">
            <h2 class="section-title">Game Actions</h2>
            <div class="text-center">
                <button class="btn-submit" onclick="startGame()">
                    <i class="fas fa-play me-2"></i>Start Game
                </button>
                <a href="{% url 'core:roguelike_setup' %}" class="btn-skip">
                    <i class="fas fa-cog me-2"></i>Custom Setup
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Game configuration from template
const gameConfig = {
    setId: {{ set_id|default:"null" }},
    gameType: '{{ game_type|default:"words" }}',
    difficulty: '{{ difficulty|default:"easy" }}',
    randomize: {{ randomize|yesno:"true,false" }},
    maxLives: {{ lives|default:3 }}
};

// Game state
let gameState = {
    score: 0,
    progress: 0,
    lives: gameConfig.maxLives,
    currentQuestion: null,
    questions: [],
    currentIndex: 0,
    isGameActive: false,
    hintsUsed: 0,
    maxHints: getMaxHints(),
    currentHint: ''
};

// Get max hints based on difficulty
function getMaxHints() {
    const hintsMap = {
        'easy': 3,
        'medium': 2,
        'hard': 1
    };
    return hintsMap[gameConfig.difficulty] || 3;
}

// Game questions will be loaded from the API
let gameQuestions = [];

async function startGame() {
    // Show loading message
    document.getElementById('question-text').textContent = 'Loading questions...';
    document.getElementById('submit-btn').disabled = true;
    document.getElementById('skip-btn').disabled = true;

    try {
        // Load questions from API
        if (!gameConfig.setId) {
            throw new Error('No content set available');
        }

        const response = await fetch(`/api/game-content/?set_id=${gameConfig.setId}&type=${gameConfig.gameType}&limit=20`);
        const data = await response.json();

        console.log('API Response:', data); // Debug log

        if (!data.success) {
            throw new Error(data.error || 'Failed to load questions');
        }

        if (!data.items || data.items.length === 0) {
            throw new Error(`No ${gameConfig.gameType} available in this set`);
        }

        // Set up game state
        gameQuestions = gameConfig.randomize ?
            data.items.sort(() => Math.random() - 0.5) :
            data.items;

        gameState.isGameActive = true;
        gameState.questions = gameQuestions;
        gameState.currentIndex = 0;
        gameState.score = 0;
        gameState.progress = 0;
        gameState.lives = gameConfig.maxLives;
        gameState.hintsUsed = 0;
        gameState.maxHints = getMaxHints();
        gameState.currentHint = '';

        updateUI();
        updateHintSection();
        loadNextQuestion();



        // Enable controls
        document.getElementById('answer-input').disabled = false;
        document.getElementById('submit-btn').disabled = false;
        document.getElementById('skip-btn').disabled = false;



        // Focus on input
        document.getElementById('answer-input').focus();

    } catch (error) {
        console.error('Error loading game:', error);
        document.getElementById('question-text').textContent = 'Error loading game';
        document.getElementById('feedback').textContent = `Error: ${error.message}. Config: ${JSON.stringify(gameConfig)}`;
        document.getElementById('feedback').className = 'feedback incorrect';
        document.getElementById('feedback').style.display = 'block';
    }
}

function loadNextQuestion() {
    console.log('Loading question:', gameState.currentIndex, 'of', gameState.questions.length); // Debug

    if (gameState.currentIndex >= gameState.questions.length) {
        endGame(true); // Victory
        return;
    }

    gameState.currentQuestion = gameState.questions[gameState.currentIndex];
    console.log('Current question:', gameState.currentQuestion); // Debug

    if (gameState.currentQuestion && gameState.currentQuestion.english) {
        document.getElementById('question-text').textContent = gameState.currentQuestion.english;
    } else {
        document.getElementById('question-text').textContent = 'Error: No question data';
        console.error('Invalid question data:', gameState.currentQuestion);
    }

    document.getElementById('answer-input').value = '';
    document.getElementById('feedback').style.display = 'none';

    // Hide continue button and ensure normal controls are enabled
    hideContinueButton();

    // Reset hint for new question
    gameState.currentHint = '';
    updateHintSection();

    // Focus on input
    setTimeout(() => {
        document.getElementById('answer-input').focus();
    }, 100);
}

function updateHintSection() {
    const hintSection = document.getElementById('hint-section');
    const hintBtn = document.getElementById('hint-btn');
    const hintCount = document.getElementById('hint-count');
    const hintDisplay = document.getElementById('hint-display');

    // Check if elements exist
    if (!hintSection || !hintBtn || !hintCount || !hintDisplay) {
        return;
    }

    // Ensure maxHints is set
    if (!gameState.maxHints || gameState.maxHints === 0) {
        gameState.maxHints = getMaxHints();
    }

    // Always show hint section during game, just manage button states
    const remainingHints = gameState.maxHints - gameState.hintsUsed;
    hintCount.textContent = remainingHints;

    if (remainingHints <= 0) {
        hintBtn.disabled = true;
        hintBtn.innerHTML = '<i class="fas fa-lightbulb me-1"></i>No hints left';
    } else {
        hintBtn.disabled = false;
        hintBtn.innerHTML = `<i class="fas fa-lightbulb me-1"></i>Get Hint (${remainingHints} left)`;
    }

    if (gameState.currentHint) {
        hintDisplay.textContent = gameState.currentHint;
        hintDisplay.style.display = 'block';
    } else {
        hintDisplay.style.display = 'none';
    }
}

// Make sure showHint is globally accessible
function showHint() {
    console.log('showHint called');

    // Ensure maxHints is set correctly
    if (!gameState.maxHints || gameState.maxHints === 0) {
        gameState.maxHints = getMaxHints();
    }

    console.log('Hints used:', gameState.hintsUsed, 'Max hints:', gameState.maxHints);

    if (gameState.hintsUsed >= gameState.maxHints) {
        console.log('No hints left');
        return;
    }

    if (!gameState.currentQuestion) {
        console.log('No current question');
        return;
    }

    const targetWord = gameState.currentQuestion.target;
    if (!targetWord) {
        console.log('No target word');
        return;
    }

    console.log('Target word:', targetWord);

    // Generate hint based on difficulty
    let hintLetters;
    switch (gameConfig.difficulty) {
        case 'easy':
            hintLetters = 3;
            break;
        case 'medium':
            hintLetters = 2;
            break;
        case 'hard':
            hintLetters = 1;
            break;
        default:
            hintLetters = 3;
    }

    console.log('Hint letters:', hintLetters);

    // Create hint with first N letters and underscores for the rest
    const hint = targetWord.substring(0, hintLetters) + '_'.repeat(Math.max(0, targetWord.length - hintLetters));

    console.log('Generated hint:', hint);

    gameState.currentHint = hint;
    gameState.hintsUsed++;

    updateHintSection();

    // Add some visual feedback
    const hintDisplay = document.getElementById('hint-display');
    if (hintDisplay) {
        hintDisplay.style.animation = 'none';
        setTimeout(() => {
            hintDisplay.style.animation = 'pulse 0.5s ease-in-out';
        }, 10);
    }

    console.log('Hint shown successfully');
}

function submitAnswer() {
    const userAnswer = document.getElementById('answer-input').value.trim().toLowerCase();
    const correctAnswer = gameState.currentQuestion.target.toLowerCase();

    const feedback = document.getElementById('feedback');

    if (userAnswer === correctAnswer) {
        // Correct answer
        gameState.score += 10;
        gameState.progress++;

        feedback.textContent = `Correct! "${gameState.currentQuestion.target}" (${gameState.currentQuestion.pronunciation})`;
        feedback.className = 'feedback correct';
        feedback.style.display = 'block';

        // Disable controls temporarily
        disableGameControls();

        setTimeout(() => {
            gameState.currentIndex++;
            loadNextQuestion();
            enableGameControls();
        }, 2000);
    } else {
        // Incorrect answer
        gameState.lives--;

        feedback.textContent = `Incorrect. The correct answer is "${gameState.currentQuestion.target}" (${gameState.currentQuestion.pronunciation})`;
        feedback.className = 'feedback incorrect';
        feedback.style.display = 'block';

        // Show continue button and disable other controls
        showContinueButton();

        if (gameState.lives <= 0) {
            // Game over - don't show continue button
            hideContinueButton();
            setTimeout(() => {
                endGame(false); // Defeat
            }, 3000);
        }
    }

    updateUI();
}

function skipQuestion() {
    gameState.currentIndex++;
    loadNextQuestion();
}

function continueAfterWrong() {
    hideContinueButton();
    enableGameControls();
    gameState.currentIndex++;
    loadNextQuestion();
}

function showContinueButton() {
    document.getElementById('continue-btn').style.display = 'inline-flex';
    document.getElementById('submit-btn').disabled = true;
    document.getElementById('skip-btn').disabled = true;
    document.getElementById('answer-input').disabled = true;
    document.getElementById('hint-btn').disabled = true;
}

function hideContinueButton() {
    document.getElementById('continue-btn').style.display = 'none';
}

function disableGameControls() {
    document.getElementById('submit-btn').disabled = true;
    document.getElementById('skip-btn').disabled = true;
    document.getElementById('answer-input').disabled = true;
    document.getElementById('hint-btn').disabled = true;
}

function enableGameControls() {
    document.getElementById('submit-btn').disabled = false;
    document.getElementById('skip-btn').disabled = false;
    document.getElementById('answer-input').disabled = false;
    // Hint button state is managed by updateHintSection()
    updateHintSection();
}

async function endGame(victory) {
    gameState.isGameActive = false;

    // Calculate final stats
    const totalQuestions = gameState.questions.length;
    const correctAnswers = gameState.score / 10; // Assuming 10 points per correct answer
    const accuracy = totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;

    const questionText = document.getElementById('question-text');
    const feedback = document.getElementById('feedback');

    if (victory) {
        questionText.textContent = "🎉 Victory! 🎉";
        feedback.innerHTML = `Congratulations! You completed the challenge!<br>Score: ${gameState.score} | Accuracy: ${accuracy.toFixed(1)}%<br><span id="completion-status">Calculating rewards...</span>`;
        feedback.className = 'feedback correct';
    } else {
        questionText.textContent = "💀 Game Over 💀";
        feedback.innerHTML = `You ran out of lives!<br>Score: ${gameState.score} | Accuracy: ${accuracy.toFixed(1)}%<br><span id="completion-status">Better luck next time!</span>`;
        feedback.className = 'feedback incorrect';
    }

    feedback.style.display = 'block';

    // Disable controls
    document.getElementById('answer-input').disabled = true;
    document.getElementById('submit-btn').disabled = true;
    document.getElementById('skip-btn').disabled = true;

    // Send results to backend for scoring/achievements
    if (victory) {
        try {
            const response = await fetch('/api/complete-game/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    difficulty: gameConfig.difficulty,
                    game_type: gameConfig.gameType,
                    set_id: gameConfig.setId,
                    score: gameState.score,
                    accuracy: accuracy,
                    completed: victory,
                    total_questions: totalQuestions
                })
            });

            const data = await response.json();

            if (data.success) {
                let statusText = '';
                if (data.points_awarded > 0) {
                    statusText += `🎉 +${data.points_awarded} XP earned!`;
                    if (data.leveled_up) {
                        statusText += `<br>🎊 Level up! You're now level ${data.new_level}!`;
                    }
                    if (data.achievements_earned.length > 0) {
                        statusText += '<br>🏆 New achievements:';
                        data.achievements_earned.forEach(achievement => {
                            statusText += `<br>${achievement.icon} ${achievement.name} (+${achievement.points} XP)`;
                        });
                    }
                } else if (data.message) {
                    statusText = data.message;
                }

                document.getElementById('completion-status').innerHTML = statusText;
            }
        } catch (error) {
            console.error('Error submitting game results:', error);
            document.getElementById('completion-status').textContent = 'Error saving results';
        }
    }
}

function updateUI() {
    document.getElementById('score').textContent = gameState.score;
    document.getElementById('progress').textContent = `${gameState.progress}/100`;
    
    // Update lives display
    const livesDisplay = document.getElementById('lives');
    livesDisplay.textContent = '❤️'.repeat(gameState.lives) + '🖤'.repeat(gameConfig.maxLives - gameState.lives);
    
    // Update progress bar
    const progressBar = document.getElementById('progress-bar');
    progressBar.style.width = `${gameState.progress}%`;
}

// Allow Enter key to submit answer
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('answer-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && gameState.isGameActive) {
            submitAnswer();
        }
    });

    // Add hint button event listener
    const hintBtn = document.getElementById('hint-btn');
    if (hintBtn) {
        hintBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Hint button clicked!');
            showHint();
        });
        console.log('Hint button event listener added');
    }

    // Auto-start the game
    console.log('Page loaded, starting game with config:', gameConfig);
    startGame();
});

// Helper function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
