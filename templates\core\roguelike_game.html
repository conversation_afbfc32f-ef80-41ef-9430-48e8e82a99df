{% extends 'base.html' %}
{% load static %}

{% block title %}Quick Game - {{ language.name }} - {{ site_settings.site_name }}{% endblock %}

{% block extra_css %}
<style>
.game-container {
    min-height: 100vh;
    padding: 100px 0 50px;
}

.game-card {
    background: rgba(15, 15, 35, 0.95);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 50px rgba(139, 92, 246, 0.2);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.game-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(251, 191, 36, 0.1), transparent);
    animation: shimmer 3s ease-in-out infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.section-title {
    font-family: 'Cinzel', serif;
    font-size: 1.8rem;
    color: var(--gold);
    margin-bottom: 1.5rem;
    text-align: center;
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.game-info {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.language-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.language-flag {
    font-size: 1.5rem;
}

.language-name {
    color: var(--text-light);
    font-weight: 600;
}

.game-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gold);
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.lives-display {
    display: flex;
    gap: 0.5rem;
    font-size: 1.5rem;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    height: 20px;
    margin: 1rem 0;
    overflow: hidden;
}

.progress-fill {
    background: linear-gradient(45deg, var(--gold), var(--dark-gold));
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0%;
}

.game-area {
    text-align: center;
    padding: 3rem 2rem;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.question-card {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.question-text {
    font-size: 2rem;
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 1rem;
}

.question-prompt {
    color: var(--text-muted);
    margin-bottom: 2rem;
}

.answer-input {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 10px;
    color: var(--text-light);
    padding: 15px 20px;
    font-size: 1.2rem;
    width: 100%;
    max-width: 400px;
    margin: 0 auto 2rem;
    transition: all 0.3s ease;
}

.answer-input:focus {
    outline: none;
    border-color: var(--gold);
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
}

.btn-submit {
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    margin: 0 0.5rem;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(139, 92, 246, 0.4);
}

.btn-skip {
    background: transparent;
    border: 2px solid var(--text-muted);
    color: var(--text-muted);
    padding: 10px 25px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    margin: 0 0.5rem;
}

.btn-skip:hover {
    border-color: var(--gold);
    color: var(--gold);
}

.feedback {
    margin-top: 2rem;
    padding: 1rem;
    border-radius: 10px;
    font-weight: 600;
    display: none;
}

.feedback.correct {
    background: rgba(34, 197, 94, 0.2);
    border: 1px solid #22c55e;
    color: #22c55e;
}

.feedback.incorrect {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid #ef4444;
    color: #ef4444;
}

.game-setup-info {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
    text-align: center;
}

.setup-title {
    color: var(--gold);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.setup-details {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.btn-back {
    background: transparent;
    border: 2px solid var(--gold);
    color: var(--gold);
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 2rem;
}

.btn-back:hover {
    background: var(--gold);
    color: var(--bg-dark);
    text-decoration: none;
    transform: translateY(-2px);
}
</style>
{% endblock %}

{% block content %}
<!-- Navigation Bar -->
{% include 'includes/navbar.html' %}

<div class="game-container">
    <div class="container">
        <!-- Back Button -->
        <a href="{% url 'core:roguelike_setup' %}" class="btn-back">
            <i class="fas fa-arrow-left me-2"></i>Back to Setup
        </a>

        <!-- Game Header -->
        <div class="game-card">
            <div class="game-header">
                <div class="game-info">
                    <div class="language-info">
                        <span class="language-flag">{{ language.flag_emoji }}</span>
                        <span class="language-name">{{ language.name }}</span>
                    </div>
                </div>
                
                <div class="game-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="score">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="progress">0/100</div>
                        <div class="stat-label">Progress</div>
                    </div>
                    <div class="stat-item">
                        <div class="lives-display" id="lives">
                            ❤️❤️❤️
                        </div>
                        <div class="stat-label">Lives</div>
                    </div>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progress-bar"></div>
            </div>
        </div>

        <!-- Game Setup Info -->
        <div class="game-card">
            <div class="game-setup-info">
                {% if quick_game %}
                    <div class="setup-title">Quick Game - {{ content_set.name }}</div>
                    <div class="setup-details">
                        Easy difficulty • {{ content_set.get_word_count }} words available • Randomized order
                    </div>
                {% else %}
                    <div class="setup-title">{{ content_set.name }}</div>
                    <div class="setup-details">
                        {{ difficulty|title }} difficulty •
                        {% if game_type == 'words' %}
                            {{ content_set.get_word_count }} words available
                        {% else %}
                            {{ content_set.get_sentence_count }} sentences available
                        {% endif %}
                        {% if randomize %} • Randomized order{% endif %}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Game Area -->
        <div class="game-card">
            <div class="game-area">
                <div class="question-card">
                    <div class="question-text" id="question-text">
                        Loading your first challenge...
                    </div>
                    <div class="question-prompt">
                        Translate this word to {{ language.name }}:
                    </div>
                    
                    <input type="text" class="answer-input" id="answer-input" placeholder="Enter your answer..." disabled>
                    
                    <div class="game-buttons">
                        <button class="btn-submit" id="submit-btn" onclick="submitAnswer()" disabled>
                            <i class="fas fa-check me-2"></i>Submit
                        </button>
                        <button class="btn-skip" id="skip-btn" onclick="skipQuestion()" disabled>
                            <i class="fas fa-forward me-2"></i>Skip
                        </button>
                    </div>
                    
                    <div class="feedback" id="feedback"></div>
                </div>
            </div>
        </div>

        <!-- Game Actions -->
        <div class="game-card">
            <h2 class="section-title">Game Actions</h2>
            <div class="text-center">
                <button class="btn-submit" onclick="startGame()">
                    <i class="fas fa-play me-2"></i>Start Game
                </button>
                <a href="{% url 'core:roguelike_setup' %}" class="btn-skip">
                    <i class="fas fa-cog me-2"></i>Custom Setup
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Game configuration from template
const gameConfig = {
    setId: {{ set_id|default:"null" }},
    gameType: '{{ game_type|default:"words" }}',
    difficulty: '{{ difficulty|default:"easy" }}',
    randomize: {{ randomize|yesno:"true,false" }},
    maxLives: {{ lives|default:3 }}
};

// Game state
let gameState = {
    score: 0,
    progress: 0,
    lives: gameConfig.maxLives,
    currentQuestion: null,
    questions: [],
    currentIndex: 0,
    isGameActive: false
};

// Game questions will be loaded from the API
let gameQuestions = [];

async function startGame() {
    // Show loading message
    document.getElementById('question-text').textContent = 'Loading questions...';
    document.getElementById('submit-btn').disabled = true;
    document.getElementById('skip-btn').disabled = true;

    try {
        // Load questions from API
        if (!gameConfig.setId) {
            throw new Error('No content set available');
        }

        const response = await fetch(`/api/game-content/?set_id=${gameConfig.setId}&type=${gameConfig.gameType}&limit=20`);
        const data = await response.json();

        if (!data.success) {
            throw new Error(data.error || 'Failed to load questions');
        }

        if (data.items.length === 0) {
            throw new Error(`No ${gameConfig.gameType} available in this set`);
        }

        // Set up game state
        gameQuestions = gameConfig.randomize ?
            data.items.sort(() => Math.random() - 0.5) :
            data.items;

        gameState.isGameActive = true;
        gameState.questions = gameQuestions;
        gameState.currentIndex = 0;
        gameState.score = 0;
        gameState.progress = 0;
        gameState.lives = gameConfig.maxLives;

        updateUI();
        loadNextQuestion();

        // Enable controls
        document.getElementById('answer-input').disabled = false;
        document.getElementById('submit-btn').disabled = false;
        document.getElementById('skip-btn').disabled = false;

        // Focus on input
        document.getElementById('answer-input').focus();

    } catch (error) {
        console.error('Error loading game:', error);
        document.getElementById('question-text').textContent = 'Error loading game';
        document.getElementById('feedback').textContent = `Error: ${error.message}. Config: ${JSON.stringify(gameConfig)}`;
        document.getElementById('feedback').className = 'feedback incorrect';
        document.getElementById('feedback').style.display = 'block';
    }
}

function loadNextQuestion() {
    if (gameState.currentIndex >= gameState.questions.length) {
        endGame(true); // Victory
        return;
    }
    
    gameState.currentQuestion = gameState.questions[gameState.currentIndex];
    document.getElementById('question-text').textContent = gameState.currentQuestion.english;
    document.getElementById('answer-input').value = '';
    document.getElementById('feedback').style.display = 'none';
    
    // Focus on input
    setTimeout(() => {
        document.getElementById('answer-input').focus();
    }, 100);
}

function submitAnswer() {
    const userAnswer = document.getElementById('answer-input').value.trim().toLowerCase();
    const correctAnswer = gameState.currentQuestion.target.toLowerCase();
    
    const feedback = document.getElementById('feedback');
    
    if (userAnswer === correctAnswer) {
        // Correct answer
        gameState.score += 10;
        gameState.progress++;
        
        feedback.textContent = `Correct! "${gameState.currentQuestion.target}" (${gameState.currentQuestion.pronunciation})`;
        feedback.className = 'feedback correct';
        feedback.style.display = 'block';
        
        setTimeout(() => {
            gameState.currentIndex++;
            loadNextQuestion();
        }, 2000);
    } else {
        // Incorrect answer
        gameState.lives--;
        
        feedback.textContent = `Incorrect. The correct answer is "${gameState.currentQuestion.target}" (${gameState.currentQuestion.pronunciation})`;
        feedback.className = 'feedback incorrect';
        feedback.style.display = 'block';
        
        if (gameState.lives <= 0) {
            setTimeout(() => {
                endGame(false); // Defeat
            }, 2000);
        } else {
            setTimeout(() => {
                gameState.currentIndex++;
                loadNextQuestion();
            }, 2000);
        }
    }
    
    updateUI();
}

function skipQuestion() {
    gameState.currentIndex++;
    loadNextQuestion();
}

function endGame(victory) {
    gameState.isGameActive = false;
    
    const questionText = document.getElementById('question-text');
    const feedback = document.getElementById('feedback');
    
    if (victory) {
        questionText.textContent = "🎉 Victory! 🎉";
        feedback.textContent = `Congratulations! You completed the challenge with a score of ${gameState.score}!`;
        feedback.className = 'feedback correct';
    } else {
        questionText.textContent = "💀 Game Over 💀";
        feedback.textContent = `You ran out of lives! Final score: ${gameState.score}. Try again!`;
        feedback.className = 'feedback incorrect';
    }
    
    feedback.style.display = 'block';
    
    // Disable controls
    document.getElementById('answer-input').disabled = true;
    document.getElementById('submit-btn').disabled = true;
    document.getElementById('skip-btn').disabled = true;
}

function updateUI() {
    document.getElementById('score').textContent = gameState.score;
    document.getElementById('progress').textContent = `${gameState.progress}/100`;
    
    // Update lives display
    const livesDisplay = document.getElementById('lives');
    livesDisplay.textContent = '❤️'.repeat(gameState.lives) + '🖤'.repeat(gameConfig.maxLives - gameState.lives);
    
    // Update progress bar
    const progressBar = document.getElementById('progress-bar');
    progressBar.style.width = `${gameState.progress}%`;
}

// Allow Enter key to submit answer
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('answer-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && gameState.isGameActive) {
            submitAnswer();
        }
    });
});
</script>
{% endblock %}
