{% extends 'base.html' %}
{% load static %}

{% block title %}Roguelike Adventure Setup - {{ language.name }} - {{ site_settings.site_name }}{% endblock %}

{% block extra_css %}
<style>
.roguelike-container {
    min-height: 100vh;
    padding: 100px 0 50px;
}

.roguelike-card {
    background: rgba(15, 15, 35, 0.95);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 50px rgba(139, 92, 246, 0.2);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.roguelike-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(251, 191, 36, 0.1), transparent);
    animation: shimmer 3s ease-in-out infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.section-title {
    font-family: 'Cinzel', serif;
    font-size: 1.8rem;
    color: var(--gold);
    margin-bottom: 1.5rem;
    text-align: center;
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
}

.game-setup-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.setup-section {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
}

.setup-title {
    color: var(--gold);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
}

.difficulty-selector {
    display: grid;
    gap: 1rem;
    margin-bottom: 1rem;
}

.difficulty-option {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 10px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.difficulty-option:hover {
    border-color: var(--gold);
    transform: translateY(-2px);
}

.difficulty-option.selected {
    border-color: var(--gold);
    background: rgba(251, 191, 36, 0.1);
}

.difficulty-name {
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.difficulty-lives {
    color: var(--gold);
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.difficulty-description {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.game-type-selector {
    display: grid;
    gap: 1rem;
    margin-bottom: 1rem;
}

.type-option {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 10px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.type-option:hover {
    border-color: var(--gold);
    transform: translateY(-2px);
}

.type-option.selected {
    border-color: var(--gold);
    background: rgba(251, 191, 36, 0.1);
}

.type-icon {
    font-size: 2rem;
    color: var(--secondary-purple);
    margin-bottom: 0.5rem;
}

.type-name {
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.type-description {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.set-selector {
    max-height: 300px;
    overflow-y: auto;
}

.set-option {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.set-option:hover {
    border-color: var(--gold);
    transform: translateY(-2px);
}

.set-option.selected {
    border-color: var(--gold);
    background: rgba(251, 191, 36, 0.1);
}

.set-name {
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.set-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.randomization-options {
    margin-top: 1rem;
}

.option-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: var(--text-light);
}

.option-checkbox input {
    margin-right: 0.5rem;
    transform: scale(1.2);
}

.btn-start-game {
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: block;
    text-align: center;
    margin: 2rem auto 0;
    max-width: 300px;
}

.btn-start-game:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(139, 92, 246, 0.4);
    color: white;
    text-decoration: none;
}

.btn-start-game:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.language-header {
    text-align: center;
    margin-bottom: 2rem;
}

.language-flag {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.language-name {
    font-size: 2rem;
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.language-description {
    color: var(--text-muted);
    font-size: 1.1rem;
}

.btn-back {
    background: transparent;
    border: 2px solid var(--gold);
    color: var(--gold);
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 2rem;
}

.btn-back:hover {
    background: var(--gold);
    color: var(--bg-dark);
    text-decoration: none;
    transform: translateY(-2px);
}
</style>
{% endblock %}

{% block content %}
<!-- Navigation Bar -->
{% include 'includes/navbar.html' %}

<div class="roguelike-container">
    <div class="container">
        <!-- Back Button -->
        <a href="{% url 'core:profile' %}" class="btn-back">
            <i class="fas fa-arrow-left me-2"></i>Back to Profile
        </a>

        <!-- Language Header -->
        <div class="roguelike-card">
            <div class="language-header">
                <div class="language-flag">{{ language.flag_emoji }}</div>
                <h1 class="language-name">{{ language.name }} Roguelike Adventure</h1>
                <p class="language-description">Configure your magical language quest</p>
            </div>
        </div>

        <!-- Game Setup -->
        <div class="roguelike-card">
            <div class="game-setup-grid">
                <!-- Difficulty Selection -->
                <div class="setup-section">
                    <h3 class="setup-title">Choose Your Challenge</h3>
                    <div class="difficulty-selector">
                        <div class="difficulty-option selected" data-difficulty="easy">
                            <div class="difficulty-name">Apprentice</div>
                            <div class="difficulty-lives">❤️❤️❤️ 3 Lives</div>
                            <div class="difficulty-description">Perfect for beginners</div>
                        </div>
                        <div class="difficulty-option" data-difficulty="medium">
                            <div class="difficulty-name">Wizard</div>
                            <div class="difficulty-lives">❤️❤️ 2 Lives</div>
                            <div class="difficulty-description">Moderate challenge</div>
                        </div>
                        <div class="difficulty-option" data-difficulty="hard">
                            <div class="difficulty-name">Archmage</div>
                            <div class="difficulty-lives">❤️ 1 Life</div>
                            <div class="difficulty-description">Ultimate test</div>
                        </div>
                    </div>
                </div>

                <!-- Game Type Selection -->
                <div class="setup-section">
                    <h3 class="setup-title">Choose Your Quest Type</h3>
                    <div class="game-type-selector">
                        <div class="type-option selected" data-type="words">
                            <div class="type-icon"><i class="fas fa-book"></i></div>
                            <div class="type-name">Word Quest</div>
                            <div class="type-description">Translate individual words</div>
                        </div>
                        <div class="type-option" data-type="sentences">
                            <div class="type-icon"><i class="fas fa-comments"></i></div>
                            <div class="type-name">Sentence Quest</div>
                            <div class="type-description">Translate full sentences</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Selection -->
            <div class="setup-section">
                <h3 class="setup-title">Select Your Content</h3>
                
                <!-- Word Sets -->
                <div id="word-sets" class="content-sets">
                    {% if word_sets %}
                        <div class="set-selector">
                            {% for word_set in word_sets %}
                            <div class="set-option {% if forloop.first %}selected{% endif %}" data-set-id="{{ word_set.id }}" data-set-type="word">
                                <div class="set-name">{{ word_set.name }}</div>
                                <div class="set-info">
                                    <span>{{ word_set.get_word_count }} words</span>
                                    <span class="difficulty-badge difficulty-{{ word_set.difficulty }}">{{ word_set.difficulty }}</span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted">
                            <p>No word sets available for {{ language.name }}</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Sentence Sets -->
                <div id="sentence-sets" class="content-sets" style="display: none;">
                    {% if sentence_sets %}
                        <div class="set-selector">
                            {% for sentence_set in sentence_sets %}
                            <div class="set-option {% if forloop.first %}selected{% endif %}" data-set-id="{{ sentence_set.id }}" data-set-type="sentence">
                                <div class="set-name">{{ sentence_set.name }}</div>
                                <div class="set-info">
                                    <span>{{ sentence_set.get_sentence_count }} sentences</span>
                                    <span class="difficulty-badge difficulty-{{ sentence_set.difficulty }}">{{ sentence_set.difficulty }}</span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted">
                            <p>No sentence sets available for {{ language.name }}</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Game Options -->
                <div class="randomization-options">
                    <div class="option-checkbox">
                        <input type="checkbox" id="randomize" checked>
                        <label for="randomize">Randomize question order</label>
                    </div>
                </div>
            </div>

            <!-- Start Game Button -->
            <button class="btn-start-game" onclick="startGame()">
                <i class="fas fa-play me-2"></i>Begin Adventure
            </button>
        </div>

        <!-- Quick Actions -->
        <div class="roguelike-card">
            <h2 class="section-title">Other Options</h2>
            <div class="text-center">
                <a href="{% url 'core:quick_game' %}" class="btn-start-game" style="display: inline-block; margin: 0.5rem;">
                    <i class="fas fa-bolt me-2"></i>Quick Game
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Game setup state
let gameConfig = {
    difficulty: 'easy',
    gameType: 'words',
    setId: null,
    randomize: true
};

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Set initial selected set
    const firstWordSet = document.querySelector('.set-option[data-set-type="word"]');
    if (firstWordSet) {
        gameConfig.setId = firstWordSet.dataset.setId;
    }
    
    setupEventListeners();
});

function setupEventListeners() {
    // Difficulty selection
    document.querySelectorAll('.difficulty-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.difficulty-option').forEach(o => o.classList.remove('selected'));
            this.classList.add('selected');
            gameConfig.difficulty = this.dataset.difficulty;
        });
    });

    // Game type selection
    document.querySelectorAll('.type-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.type-option').forEach(o => o.classList.remove('selected'));
            this.classList.add('selected');
            gameConfig.gameType = this.dataset.type;
            
            // Show/hide appropriate content sets
            if (gameConfig.gameType === 'words') {
                document.getElementById('word-sets').style.display = 'block';
                document.getElementById('sentence-sets').style.display = 'none';
                const firstWordSet = document.querySelector('.set-option[data-set-type="word"]');
                if (firstWordSet) {
                    gameConfig.setId = firstWordSet.dataset.setId;
                }
            } else {
                document.getElementById('word-sets').style.display = 'none';
                document.getElementById('sentence-sets').style.display = 'block';
                const firstSentenceSet = document.querySelector('.set-option[data-set-type="sentence"]');
                if (firstSentenceSet) {
                    gameConfig.setId = firstSentenceSet.dataset.setId;
                }
            }
        });
    });

    // Set selection
    document.querySelectorAll('.set-option').forEach(option => {
        option.addEventListener('click', function() {
            // Remove selection from sets of the same type
            const setType = this.dataset.setType;
            document.querySelectorAll(`.set-option[data-set-type="${setType}"]`).forEach(o => o.classList.remove('selected'));
            this.classList.add('selected');
            gameConfig.setId = this.dataset.setId;
        });
    });

    // Randomization option
    document.getElementById('randomize').addEventListener('change', function() {
        gameConfig.randomize = this.checked;
    });
}

function startGame() {
    if (!gameConfig.setId) {
        alert('Please select a content set to begin the game.');
        return;
    }

    // Build the game URL with parameters
    const gameUrl = `/game/play/?difficulty=${gameConfig.difficulty}&type=${gameConfig.gameType}&set=${gameConfig.setId}&randomize=${gameConfig.randomize}`;

    console.log('Starting game with URL:', gameUrl);
    console.log('Game config:', gameConfig);

    // Redirect to the game
    window.location.href = gameUrl;
}
</script>
{% endblock %}
