<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 Bulgarian Language Game</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .game-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .game-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .game-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 18px;
        }
        .question-area {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
        }
        .question-text {
            font-size: 24px;
            margin-bottom: 20px;
            min-height: 60px;
        }
        .answer-input {
            width: 100%;
            max-width: 400px;
            padding: 15px;
            font-size: 18px;
            border: none;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        .game-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .btn {
            padding: 12px 24px;
            font-size: 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #4CAF50; color: white; }
        .btn-secondary { background: #2196F3; color: white; }
        .btn-warning { background: #FF9800; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-size: 16px;
        }
        .feedback.correct { background: rgba(76, 175, 80, 0.3); }
        .feedback.incorrect { background: rgba(244, 67, 54, 0.3); }
        .feedback.info { background: rgba(33, 150, 243, 0.3); }
        .hint-area {
            background: rgba(255, 193, 7, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            display: none;
        }
        .language-selection {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            text-align: center;
        }
        .language-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .language-pair label {
            display: block;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .language-pair label:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        .language-pair input[type="radio"] {
            margin-right: 10px;
        }
        .game-type-selection, .difficulty-selection {
            margin: 20px 0;
        }
        .game-type-selection label, .difficulty-selection label {
            display: inline-block;
            margin: 0 15px;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .game-type-selection label:hover, .difficulty-selection label:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        .game-area {
            display: none;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>🎮 Language Learning Game</h1>
            <p>Choose your language pair and start learning!</p>
        </div>

        <div class="language-selection" id="language-selection">
            <h2>🌍 Select Language Pair</h2>
            <div class="language-options">
                <div class="language-pair">
                    <label>
                        <input type="radio" name="language-pair" value="bg-en" checked>
                        🇧🇬 Bulgarian → 🇬🇧 English
                    </label>
                </div>
                <div class="language-pair">
                    <label>
                        <input type="radio" name="language-pair" value="en-bg">
                        🇬🇧 English → 🇧🇬 Bulgarian
                    </label>
                </div>
                <div class="language-pair">
                    <label>
                        <input type="radio" name="language-pair" value="pl-en">
                        🇵🇱 Polish → 🇬🇧 English
                    </label>
                </div>
                <div class="language-pair">
                    <label>
                        <input type="radio" name="language-pair" value="en-pl">
                        🇬🇧 English → 🇵🇱 Polish
                    </label>
                </div>
                <div class="language-pair">
                    <label>
                        <input type="radio" name="language-pair" value="de-en">
                        🇩🇪 German → 🇬🇧 English
                    </label>
                </div>
                <div class="language-pair">
                    <label>
                        <input type="radio" name="language-pair" value="en-de">
                        🇬🇧 English → 🇩🇪 German
                    </label>
                </div>
            </div>

            <div class="game-type-selection">
                <h3>📚 Content Type</h3>
                <label>
                    <input type="radio" name="content-type" value="words" checked>
                    📝 Words
                </label>
                <label>
                    <input type="radio" name="content-type" value="sentences">
                    📖 Sentences
                </label>
            </div>

            <div class="difficulty-selection">
                <h3>⚡ Difficulty</h3>
                <label>
                    <input type="radio" name="difficulty" value="easy" checked>
                    😊 Easy (3 lives)
                </label>
                <label>
                    <input type="radio" name="difficulty" value="medium">
                    😐 Medium (2 lives)
                </label>
                <label>
                    <input type="radio" name="difficulty" value="hard">
                    😤 Hard (1 life)
                </label>
            </div>

            <button class="btn btn-primary" onclick="startSelectedGame()">🚀 Start Game</button>
        </div>
        
        <div class="game-area" id="game-area">
            <div class="game-stats">
                <div>❤️ Lives: <span id="lives">3</span></div>
                <div>⭐ Score: <span id="score">0</span></div>
                <div>📊 Progress: <span id="progress">0/10</span></div>
            </div>
        
        <div class="question-area">
            <div id="question-text" class="question-text">🔄 Loading your first challenge...</div>
            <input type="text" id="answer-input" class="answer-input" placeholder="Enter your answer..." disabled>
            
            <div class="game-buttons">
                <button id="submit-btn" class="btn btn-primary" onclick="submitAnswer()" disabled>✅ Submit</button>
                <button id="hint-btn" class="btn btn-warning" onclick="showHint()">💡 Hint</button>
                <button id="skip-btn" class="btn btn-secondary" onclick="skipQuestion()">⏭️ Skip</button>
            </div>
            
            <div id="hint-area" class="hint-area">
                <strong>💡 Hint:</strong> <span id="hint-text"></span>
            </div>
            </div>

            <div id="feedback" class="feedback" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Game state
        let gameQuestions = [];
        let currentQuestionIndex = 0;
        let lives = 3;
        let score = 0;
        let currentQuestion = null;
        let hintsUsed = 0;
        let gameConfig = {
            languagePair: 'bg-en',
            contentType: 'words',
            difficulty: 'easy',
            setId: null,
            sourceLanguage: 'bg',
            targetLanguage: 'en'
        };

        // Language to set ID mapping
        const languageSetMapping = {
            'bg': { words: [33, 34, 35, 36, 37, 38, 39, 40, 41, 42], sentences: [28, 29, 30, 31, 32] },
            'pl': { words: [43, 44, 45, 46, 47], sentences: [48, 49] },
            'de': { words: [50, 51, 52, 53], sentences: [54, 55] }
        };

        // Start the game when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎮 Game page loaded!');
            // Don't auto-start, wait for user selection
        });

        // Start game with selected options
        function startSelectedGame() {
            // Get selected options
            const languagePair = document.querySelector('input[name="language-pair"]:checked').value;
            const contentType = document.querySelector('input[name="content-type"]:checked').value;
            const difficulty = document.querySelector('input[name="difficulty"]:checked').value;

            // Parse language pair
            const [sourceLanguage, targetLanguage] = languagePair.split('-');

            // Update game config
            gameConfig.languagePair = languagePair;
            gameConfig.contentType = contentType;
            gameConfig.difficulty = difficulty;
            gameConfig.sourceLanguage = sourceLanguage;
            gameConfig.targetLanguage = targetLanguage;

            // Set lives based on difficulty
            lives = difficulty === 'easy' ? 3 : difficulty === 'medium' ? 2 : 1;

            // Get appropriate set ID
            if (sourceLanguage !== 'en') {
                // Foreign language to English
                const sets = languageSetMapping[sourceLanguage];
                if (sets && sets[contentType] && sets[contentType].length > 0) {
                    gameConfig.setId = sets[contentType][0]; // Use first available set
                }
            } else {
                // English to foreign language (reverse)
                const sets = languageSetMapping[targetLanguage];
                if (sets && sets[contentType] && sets[contentType].length > 0) {
                    gameConfig.setId = sets[contentType][0]; // Use first available set
                }
            }

            console.log('Starting game with config:', gameConfig);

            // Hide language selection and show game area
            document.getElementById('language-selection').style.display = 'none';
            document.getElementById('game-area').style.display = 'block';

            // Start the game
            startGame();
        }

        // Start game function
        async function startGame() {
            console.log('🚀 Starting game...');

            try {
                // Show loading message
                const languageNames = {
                    'bg': 'Bulgarian', 'pl': 'Polish', 'de': 'German', 'en': 'English'
                };
                const sourceLang = languageNames[gameConfig.sourceLanguage];
                const targetLang = languageNames[gameConfig.targetLanguage];

                document.getElementById('question-text').textContent =
                    `🔄 Loading ${sourceLang} ${gameConfig.contentType}...`;

                // Fetch questions from API
                const apiUrl = `/api/game-content/?set_id=${gameConfig.setId}&type=${gameConfig.contentType}&limit=10`;
                console.log('API URL:', apiUrl);
                const response = await fetch(apiUrl);
                console.log('API Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('API Data:', data);
                
                if (data.success && data.items && data.items.length > 0) {
                    gameQuestions = data.items;
                    currentQuestionIndex = 0;
                    lives = 3;
                    score = 0;
                    hintsUsed = 0;
                    
                    updateDisplay();
                    loadQuestion();
                    
                    // Enable input and buttons
                    document.getElementById('answer-input').disabled = false;
                    document.getElementById('submit-btn').disabled = false;
                    
                    showFeedback('🎮 Game started! Translate the Bulgarian words to English.', 'info');
                } else {
                    throw new Error('No questions received from API');
                }
                
            } catch (error) {
                console.error('❌ Error starting game:', error);
                document.getElementById('question-text').textContent = '❌ Error loading game';
                showFeedback(`Error: ${error.message}. Please refresh the page.`, 'incorrect');
            }
        }

        // Load current question
        function loadQuestion() {
            if (currentQuestionIndex < gameQuestions.length) {
                currentQuestion = gameQuestions[currentQuestionIndex];

                // Determine source and target text based on language direction
                let sourceText, targetText, hintText;

                if (gameConfig.sourceLanguage === 'en') {
                    // English to foreign language
                    sourceText = currentQuestion.english;
                    targetText = currentQuestion.target;
                    hintText = currentQuestion.target;
                } else {
                    // Foreign language to English
                    sourceText = currentQuestion.target;
                    targetText = currentQuestion.english;
                    hintText = currentQuestion.english;
                }

                const languageNames = {
                    'bg': 'Bulgarian', 'pl': 'Polish', 'de': 'German', 'en': 'English'
                };
                const targetLang = languageNames[gameConfig.targetLanguage];

                document.getElementById('question-text').innerHTML = `
                    Translate this ${gameConfig.contentType.slice(0, -1)} to ${targetLang}:<br>
                    <strong>"${sourceText}"</strong><br>
                    ${currentQuestion.pronunciation ? `<small>(${currentQuestion.pronunciation})</small>` : ''}
                `;

                // Store the correct answer for checking
                currentQuestion.correctAnswer = targetText;
                currentQuestion.hintText = hintText;

                document.getElementById('answer-input').value = '';
                document.getElementById('answer-input').focus();
                document.getElementById('hint-area').style.display = 'none';
                updateDisplay();
            } else {
                endGame();
            }
        }

        // Submit answer
        function submitAnswer() {
            const answer = document.getElementById('answer-input').value.trim().toLowerCase();
            if (!answer) return;

            const correct = currentQuestion.correctAnswer.toLowerCase();

            if (answer === correct) {
                score++;
                const sourceText = gameConfig.sourceLanguage === 'en' ? currentQuestion.english : currentQuestion.target;
                const targetText = currentQuestion.correctAnswer;
                showFeedback(`✅ Correct! "${sourceText}" = "${targetText}"`, 'correct');
                setTimeout(nextQuestion, 1500);
            } else {
                lives--;
                const sourceText = gameConfig.sourceLanguage === 'en' ? currentQuestion.english : currentQuestion.target;
                showFeedback(`❌ Incorrect! Your answer: "${answer}"<br>Correct answer: "${currentQuestion.correctAnswer}"`, 'incorrect');

                if (lives <= 0) {
                    setTimeout(endGame, 2000);
                } else {
                    setTimeout(nextQuestion, 2500);
                }
            }

            updateDisplay();
        }

        // Show hint
        function showHint() {
            if (!currentQuestion) return;

            const hintText = currentQuestion.hintText;
            const hint = hintText.substring(0, Math.min(2, hintText.length)) + '...';

            document.getElementById('hint-text').textContent = hint;
            document.getElementById('hint-area').style.display = 'block';
            hintsUsed++;
        }

        // Skip question
        function skipQuestion() {
            showFeedback(`⏭️ Skipped! The answer was: "${currentQuestion.correctAnswer}"`, 'info');
            setTimeout(nextQuestion, 1500);
        }

        // Next question
        function nextQuestion() {
            currentQuestionIndex++;
            loadQuestion();
            hideFeedback();
        }

        // Update display
        function updateDisplay() {
            document.getElementById('lives').textContent = lives;
            document.getElementById('score').textContent = score;
            document.getElementById('progress').textContent = `${currentQuestionIndex + 1}/${gameQuestions.length}`;
        }

        // Show feedback
        function showFeedback(message, type) {
            const feedback = document.getElementById('feedback');
            feedback.innerHTML = message;
            feedback.className = `feedback ${type}`;
            feedback.style.display = 'block';
        }

        // Hide feedback
        function hideFeedback() {
            document.getElementById('feedback').style.display = 'none';
        }

        // End game
        function endGame() {
            const finalScore = score;
            const totalQuestions = gameQuestions.length;
            const percentage = Math.round((finalScore / totalQuestions) * 100);
            
            document.getElementById('question-text').innerHTML = `
                🎉 Game Complete!<br>
                <strong>Final Score: ${finalScore}/${totalQuestions} (${percentage}%)</strong>
            `;
            
            document.getElementById('answer-input').disabled = true;
            document.getElementById('submit-btn').disabled = true;
            
            if (lives <= 0) {
                showFeedback('💀 Game Over! You ran out of lives.', 'incorrect');
            } else {
                showFeedback('🎉 Congratulations! You completed all questions!', 'correct');
            }
        }

        // Enter key support
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !document.getElementById('submit-btn').disabled) {
                submitAnswer();
            }
        });
    </script>
</body>
</html>
