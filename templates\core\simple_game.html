<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 Bulgarian Language Game</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .game-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .game-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .game-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 18px;
        }
        .question-area {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
        }
        .question-text {
            font-size: 24px;
            margin-bottom: 20px;
            min-height: 60px;
        }
        .answer-input {
            width: 100%;
            max-width: 400px;
            padding: 15px;
            font-size: 18px;
            border: none;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        .game-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .btn {
            padding: 12px 24px;
            font-size: 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #4CAF50; color: white; }
        .btn-secondary { background: #2196F3; color: white; }
        .btn-warning { background: #FF9800; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-size: 16px;
        }
        .feedback.correct { background: rgba(76, 175, 80, 0.3); }
        .feedback.incorrect { background: rgba(244, 67, 54, 0.3); }
        .feedback.info { background: rgba(33, 150, 243, 0.3); }
        .hint-area {
            background: rgba(255, 193, 7, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>🎮 Bulgarian Language Game</h1>
            <p>Translate Bulgarian words to English</p>
        </div>
        
        <div class="game-stats">
            <div>❤️ Lives: <span id="lives">3</span></div>
            <div>⭐ Score: <span id="score">0</span></div>
            <div>📊 Progress: <span id="progress">0/10</span></div>
        </div>
        
        <div class="question-area">
            <div id="question-text" class="question-text">🔄 Loading your first challenge...</div>
            <input type="text" id="answer-input" class="answer-input" placeholder="Enter your answer..." disabled>
            
            <div class="game-buttons">
                <button id="submit-btn" class="btn btn-primary" onclick="submitAnswer()" disabled>✅ Submit</button>
                <button id="hint-btn" class="btn btn-warning" onclick="showHint()">💡 Hint</button>
                <button id="skip-btn" class="btn btn-secondary" onclick="skipQuestion()">⏭️ Skip</button>
            </div>
            
            <div id="hint-area" class="hint-area">
                <strong>💡 Hint:</strong> <span id="hint-text"></span>
            </div>
        </div>
        
        <div id="feedback" class="feedback" style="display: none;"></div>
    </div>

    <script>
        // Game state
        let gameQuestions = [];
        let currentQuestionIndex = 0;
        let lives = 3;
        let score = 0;
        let currentQuestion = null;
        let hintsUsed = 0;

        // Start the game when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎮 Game page loaded!');
            startGame();
        });

        // Start game function
        async function startGame() {
            console.log('🚀 Starting game...');
            
            try {
                // Show loading message
                document.getElementById('question-text').textContent = '🔄 Loading Bulgarian words...';
                
                // Fetch questions from API
                const response = await fetch('/api/game-content/?set_id=33&type=words&limit=10');
                console.log('API Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('API Data:', data);
                
                if (data.success && data.items && data.items.length > 0) {
                    gameQuestions = data.items;
                    currentQuestionIndex = 0;
                    lives = 3;
                    score = 0;
                    hintsUsed = 0;
                    
                    updateDisplay();
                    loadQuestion();
                    
                    // Enable input and buttons
                    document.getElementById('answer-input').disabled = false;
                    document.getElementById('submit-btn').disabled = false;
                    
                    showFeedback('🎮 Game started! Translate the Bulgarian words to English.', 'info');
                } else {
                    throw new Error('No questions received from API');
                }
                
            } catch (error) {
                console.error('❌ Error starting game:', error);
                document.getElementById('question-text').textContent = '❌ Error loading game';
                showFeedback(`Error: ${error.message}. Please refresh the page.`, 'incorrect');
            }
        }

        // Load current question
        function loadQuestion() {
            if (currentQuestionIndex < gameQuestions.length) {
                currentQuestion = gameQuestions[currentQuestionIndex];
                document.getElementById('question-text').innerHTML = `
                    Translate this word to English:<br>
                    <strong>"${currentQuestion.target}"</strong><br>
                    <small>(${currentQuestion.pronunciation})</small>
                `;
                document.getElementById('answer-input').value = '';
                document.getElementById('answer-input').focus();
                document.getElementById('hint-area').style.display = 'none';
                updateDisplay();
            } else {
                endGame();
            }
        }

        // Submit answer
        function submitAnswer() {
            const answer = document.getElementById('answer-input').value.trim().toLowerCase();
            if (!answer) return;
            
            const correct = currentQuestion.english.toLowerCase();
            
            if (answer === correct) {
                score++;
                showFeedback(`✅ Correct! "${currentQuestion.target}" = "${currentQuestion.english}"`, 'correct');
                setTimeout(nextQuestion, 1500);
            } else {
                lives--;
                showFeedback(`❌ Incorrect! Your answer: "${answer}"<br>Correct answer: "${currentQuestion.english}"`, 'incorrect');
                
                if (lives <= 0) {
                    setTimeout(endGame, 2000);
                } else {
                    setTimeout(nextQuestion, 2500);
                }
            }
            
            updateDisplay();
        }

        // Show hint
        function showHint() {
            if (!currentQuestion) return;
            
            const english = currentQuestion.english;
            const hintText = english.substring(0, Math.min(2, english.length)) + '...';
            
            document.getElementById('hint-text').textContent = hintText;
            document.getElementById('hint-area').style.display = 'block';
            hintsUsed++;
        }

        // Skip question
        function skipQuestion() {
            showFeedback(`⏭️ Skipped! The answer was: "${currentQuestion.english}"`, 'info');
            setTimeout(nextQuestion, 1500);
        }

        // Next question
        function nextQuestion() {
            currentQuestionIndex++;
            loadQuestion();
            hideFeedback();
        }

        // Update display
        function updateDisplay() {
            document.getElementById('lives').textContent = lives;
            document.getElementById('score').textContent = score;
            document.getElementById('progress').textContent = `${currentQuestionIndex + 1}/${gameQuestions.length}`;
        }

        // Show feedback
        function showFeedback(message, type) {
            const feedback = document.getElementById('feedback');
            feedback.innerHTML = message;
            feedback.className = `feedback ${type}`;
            feedback.style.display = 'block';
        }

        // Hide feedback
        function hideFeedback() {
            document.getElementById('feedback').style.display = 'none';
        }

        // End game
        function endGame() {
            const finalScore = score;
            const totalQuestions = gameQuestions.length;
            const percentage = Math.round((finalScore / totalQuestions) * 100);
            
            document.getElementById('question-text').innerHTML = `
                🎉 Game Complete!<br>
                <strong>Final Score: ${finalScore}/${totalQuestions} (${percentage}%)</strong>
            `;
            
            document.getElementById('answer-input').disabled = true;
            document.getElementById('submit-btn').disabled = true;
            
            if (lives <= 0) {
                showFeedback('💀 Game Over! You ran out of lives.', 'incorrect');
            } else {
                showFeedback('🎉 Congratulations! You completed all questions!', 'correct');
            }
        }

        // Enter key support
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !document.getElementById('submit-btn').disabled) {
                submitAnswer();
            }
        });
    </script>
</body>
</html>
