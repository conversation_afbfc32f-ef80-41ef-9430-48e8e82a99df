<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulgarian Learning Game - Working Version</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            min-height: 100vh;
        }
        .game-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
        }
        .question {
            font-size: 2.5rem;
            color: #FFD700;
            margin: 20px 0;
            font-weight: bold;
        }
        .instruction {
            font-size: 1.2rem;
            margin: 10px 0;
        }
        .pronunciation {
            font-size: 1rem;
            color: #87CEEB;
            font-style: italic;
            margin: 10px 0;
        }
        .input-area {
            margin: 30px 0;
        }
        .answer-input {
            padding: 15px;
            font-size: 1.2rem;
            border: none;
            border-radius: 8px;
            width: 300px;
            text-align: center;
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .feedback {
            margin: 20px 0;
            font-size: 1.2rem;
            min-height: 30px;
        }
        .correct {
            color: #90EE90;
        }
        .incorrect {
            color: #FFB6C1;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            font-size: 1.1rem;
        }
        .loading {
            color: #FFD700;
            font-size: 1.2rem;
        }
        .error {
            color: #FF6B6B;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🎯 Bulgarian Learning Game</h1>
        <p>Set: {{ set_name }} | Type: {{ game_type }} | Difficulty: {{ difficulty }}</p>
        
        <div class="stats">
            <div>Score: <span id="score">0</span></div>
            <div>Lives: <span id="lives">❤️❤️❤️</span></div>
            <div>Progress: <span id="progress">0</span>/<span id="total">0</span></div>
        </div>
        
        <div id="game-area">
            <div class="instruction" id="instruction">Translate this English word to Bulgarian:</div>
            <div class="question" id="question">Loading...</div>
            <div class="pronunciation" id="pronunciation"></div>
            
            <div class="input-area">
                <input type="text" id="answer" class="answer-input" placeholder="Type Bulgarian translation..." disabled>
            </div>
            
            <div>
                <button id="submit-btn" class="btn" onclick="submitAnswer()" disabled>Submit</button>
                <button id="hint-btn" class="btn" onclick="showHint()" disabled>💡 Hint</button>
                <button id="skip-btn" class="btn" onclick="skipQuestion()" disabled>⏭️ Skip</button>
            </div>
            
            <div class="feedback" id="feedback"></div>
        </div>
    </div>

    <script>
        // Game state
        let gameData = {
            words: [],
            currentIndex: 0,
            score: 0,
            lives: 3,
            currentWord: null,
            hintsUsed: 0,
            maxHints: 3
        };

        // Load words from API
        async function loadWords() {
            try {
                console.log('🔄 Loading words from API...');
                document.getElementById('question').textContent = 'Loading words from database...';
                
                const response = await fetch('/api/game-content/?type={{ game_type }}&set={{ set_id }}&limit=20');
                console.log('📡 Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('📦 API Response:', data);
                
                if (!data.success || !data.items || data.items.length === 0) {
                    throw new Error('No words available');
                }
                
                gameData.words = data.items;
                document.getElementById('total').textContent = gameData.words.length;
                
                console.log('✅ Loaded', gameData.words.length, 'words');
                console.log('✅ First word:', gameData.words[0]);
                
                // Enable controls and start game
                document.getElementById('answer').disabled = false;
                document.getElementById('submit-btn').disabled = false;
                document.getElementById('hint-btn').disabled = false;
                document.getElementById('skip-btn').disabled = false;
                
                showCurrentWord();
                
            } catch (error) {
                console.error('❌ Error loading words:', error);
                document.getElementById('question').textContent = 'Error loading words';
                document.getElementById('feedback').innerHTML = `<span class="error">Error: ${error.message}</span>`;
            }
        }

        function showCurrentWord() {
            if (gameData.currentIndex >= gameData.words.length) {
                endGame();
                return;
            }
            
            gameData.currentWord = gameData.words[gameData.currentIndex];
            console.log('📝 Current word:', gameData.currentWord);
            
            // Show English word
            const englishWord = gameData.currentWord.english || gameData.currentWord.english_word || 'Unknown';
            document.getElementById('question').textContent = englishWord;
            
            // Show pronunciation if available
            const pronunciation = gameData.currentWord.pronunciation || '';
            document.getElementById('pronunciation').textContent = pronunciation ? `(${pronunciation})` : '';
            
            // Clear input and feedback
            document.getElementById('answer').value = '';
            document.getElementById('feedback').innerHTML = '';
            
            // Update progress
            document.getElementById('progress').textContent = gameData.currentIndex + 1;
            
            // Focus on input
            document.getElementById('answer').focus();
        }

        function submitAnswer() {
            const userAnswer = document.getElementById('answer').value.trim().toLowerCase();
            const correctAnswer = (gameData.currentWord.target || gameData.currentWord.target_word || '').toLowerCase();
            
            console.log('🎯 User answer:', userAnswer);
            console.log('🎯 Correct answer:', correctAnswer);
            
            if (userAnswer === correctAnswer) {
                gameData.score += 10;
                document.getElementById('score').textContent = gameData.score;
                document.getElementById('feedback').innerHTML = `<span class="correct">✅ Correct! "${correctAnswer}"</span>`;
                
                setTimeout(() => {
                    gameData.currentIndex++;
                    showCurrentWord();
                }, 1500);
            } else {
                gameData.lives--;
                document.getElementById('lives').textContent = '❤️'.repeat(gameData.lives);
                document.getElementById('feedback').innerHTML = `<span class="incorrect">❌ Incorrect. Answer: "${correctAnswer}"</span>`;
                
                if (gameData.lives <= 0) {
                    setTimeout(endGame, 2000);
                } else {
                    setTimeout(() => {
                        gameData.currentIndex++;
                        showCurrentWord();
                    }, 2000);
                }
            }
        }

        function showHint() {
            if (gameData.hintsUsed >= gameData.maxHints) {
                document.getElementById('feedback').innerHTML = '<span class="error">No hints left!</span>';
                return;
            }
            
            const correctAnswer = gameData.currentWord.target || gameData.currentWord.target_word || '';
            const hintLength = Math.max(1, Math.floor(correctAnswer.length / 3));
            const hint = correctAnswer.substring(0, hintLength) + '...';
            
            gameData.hintsUsed++;
            document.getElementById('feedback').innerHTML = `<span style="color: #FFD700;">💡 Hint: ${hint}</span>`;
        }

        function skipQuestion() {
            const correctAnswer = gameData.currentWord.target || gameData.currentWord.target_word || '';
            document.getElementById('feedback').innerHTML = `<span style="color: #FFA500;">⏭️ Skipped. Answer: "${correctAnswer}"</span>`;
            
            setTimeout(() => {
                gameData.currentIndex++;
                showCurrentWord();
            }, 1500);
        }

        function endGame() {
            document.getElementById('question').textContent = 'Game Over!';
            document.getElementById('pronunciation').textContent = '';
            document.getElementById('feedback').innerHTML = `<strong>Final Score: ${gameData.score}</strong>`;
            
            // Disable controls
            document.getElementById('answer').disabled = true;
            document.getElementById('submit-btn').disabled = true;
            document.getElementById('hint-btn').disabled = true;
            document.getElementById('skip-btn').disabled = true;
        }

        // Handle Enter key
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !document.getElementById('submit-btn').disabled) {
                submitAnswer();
            }
        });

        // Start the game when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎮 Page loaded, starting game...');
            loadWords();
        });
    </script>
</body>
</html>
