{% extends 'base.html' %}
{% load static %}

{% block title %}Study Words - {{ language.name }} - {{ site_settings.site_name }}{% endblock %}

{% block extra_css %}
<style>
.study-container {
    min-height: 100vh;
    padding: 100px 0 50px;
}

.study-card {
    background: rgba(15, 15, 35, 0.95);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 50px rgba(139, 92, 246, 0.2);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.study-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(251, 191, 36, 0.1), transparent);
    animation: shimmer 3s ease-in-out infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.section-title {
    font-family: 'Cinzel', serif;
    font-size: 1.8rem;
    color: var(--gold);
    margin-bottom: 1.5rem;
    text-align: center;
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
}

.word-set-card {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(139, 92, 246, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.word-set-card:hover {
    border-color: var(--gold);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
}

.word-set-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.word-set-title {
    font-size: 1.3rem;
    color: var(--text-light);
    font-weight: 600;
}

.difficulty-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-easy {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid #22c55e;
}

.difficulty-medium {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border: 1px solid #fbbf24;
}

.difficulty-hard {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid #ef4444;
}

.word-set-description {
    color: var(--text-muted);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.word-set-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.word-count {
    color: var(--gold);
    font-weight: 600;
}

.btn-study {
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 0.9rem;
}

.btn-study:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.4);
    color: white;
    text-decoration: none;
}

.language-header {
    text-align: center;
    margin-bottom: 2rem;
}

.language-flag {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.language-name {
    font-size: 2rem;
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.language-description {
    color: var(--text-muted);
    font-size: 1.1rem;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 4rem;
    color: var(--primary-purple);
    margin-bottom: 1rem;
}

.btn-back {
    background: transparent;
    border: 2px solid var(--gold);
    color: var(--gold);
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 2rem;
}

.btn-back:hover {
    background: var(--gold);
    color: var(--bg-dark);
    text-decoration: none;
    transform: translateY(-2px);
}
</style>
{% endblock %}

{% block content %}
<!-- Navigation Bar -->
{% include 'includes/navbar.html' %}

<div class="study-container">
    <div class="container">
        <!-- Back Button -->
        <a href="{% url 'core:profile' %}" class="btn-back">
            <i class="fas fa-arrow-left me-2"></i>Back to Profile
        </a>

        <!-- Language Header -->
        <div class="study-card">
            <div class="language-header">
                <div class="language-flag">{{ language.flag_emoji }}</div>
                <h1 class="language-name">{{ language.name }} Words</h1>
                <p class="language-description">Master the vocabulary of {{ language.name }}</p>
            </div>
        </div>

        <!-- Word Sets -->
        {% if word_sets %}
            {% for word_set in word_sets %}
            <div class="study-card">
                <div class="word-set-card" onclick="location.href='{% url 'core:word_set_detail' word_set.id %}'">
                    <div class="word-set-header">
                        <h3 class="word-set-title">{{ word_set.name }}</h3>
                        <span class="difficulty-badge difficulty-{{ word_set.difficulty }}">
                            {{ word_set.difficulty }}
                        </span>
                    </div>

                    {% if word_set.description %}
                    <p class="word-set-description">{{ word_set.description }}</p>
                    {% endif %}

                    <div class="word-set-stats">
                        <span class="word-count">
                            <i class="fas fa-book me-1"></i>
                            {{ word_set.get_word_count }} words
                        </span>
                        <a href="{% url 'core:word_set_detail' word_set.id %}" class="btn-study">
                            <i class="fas fa-play me-1"></i>Study Now
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="study-card">
                <div class="empty-state">
                    <i class="fas fa-book-open"></i>
                    <h3>No Word Sets Available</h3>
                    <p>There are no word sets available for {{ language.name }} yet.</p>
                    <p>Check back later or contact an administrator to add content.</p>
                </div>
            </div>
        {% endif %}

        <!-- Quick Actions -->
        <div class="study-card">
            <h2 class="section-title">Other Learning Options</h2>
            <div class="text-center">
                <a href="{% url 'core:study_sentences' %}" class="btn-study me-3">
                    <i class="fas fa-comments me-2"></i>Practice Sentences
                </a>
                <a href="{% url 'core:flashcards' %}" class="btn-study me-3">
                    <i class="fas fa-cards-blank me-2"></i>Flashcards
                </a>
                <a href="{% url 'core:roguelike_setup' %}" class="btn-study">
                    <i class="fas fa-gamepad me-2"></i>Play Game
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
