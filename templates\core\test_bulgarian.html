<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulgarian Test Game - GUARANTEED TO WORK</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .game-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
        }
        .bulgarian-word {
            font-size: 3rem;
            font-weight: bold;
            color: #FFD700;
            margin: 20px 0;
        }
        .english-word {
            font-size: 1.5rem;
            color: #87CEEB;
            margin: 10px 0;
        }
        .pronunciation {
            font-size: 1.2rem;
            color: #98FB98;
            font-style: italic;
        }
        .word-card {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
        }
        .btn:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🎯 BULGARIAN LANGUAGE TEST - GUARANTEED WORKING!</h1>
        <p>This page will definitely show Bulgarian words!</p>
        
        <div class="word-card">
            <div class="bulgarian-word">прозорец</div>
            <div class="english-word">window</div>
            <div class="pronunciation">pro-zo-rets</div>
        </div>
        
        <div class="word-card">
            <div class="bulgarian-word">какво</div>
            <div class="english-word">what</div>
            <div class="pronunciation">kak-vo</div>
        </div>
        
        <div class="word-card">
            <div class="bulgarian-word">къща</div>
            <div class="english-word">house</div>
            <div class="pronunciation">kash-ta</div>
        </div>
        
        <div class="word-card">
            <div class="bulgarian-word">вода</div>
            <div class="english-word">water</div>
            <div class="pronunciation">vo-da</div>
        </div>
        
        <div class="word-card">
            <div class="bulgarian-word">време</div>
            <div class="english-word">time</div>
            <div class="pronunciation">vre-me</div>
        </div>
        
        <h2>🎮 Interactive Game</h2>
        <div id="game-area">
            <div class="word-card">
                <div class="bulgarian-word" id="current-word">прозорец</div>
                <div class="pronunciation" id="current-pronunciation">pro-zo-rets</div>
                <input type="text" id="answer-input" placeholder="Type the English translation..." 
                       style="padding: 10px; font-size: 1.1rem; border-radius: 5px; border: none; margin: 10px;">
                <br>
                <button class="btn" onclick="checkAnswer()">Check Answer</button>
                <button class="btn" onclick="nextWord()">Next Word</button>
            </div>
            <div id="feedback" style="margin-top: 20px; font-size: 1.2rem;"></div>
        </div>
        
        <p style="margin-top: 30px;">
            <strong>✅ If you can see Bulgarian words above, the system is working!</strong><br>
            <strong>❌ If you see question marks or boxes, there's a font/encoding issue.</strong>
        </p>
    </div>

    <script>
        console.log('🎯 Bulgarian test page loaded successfully!');
        
        const words = [
            {bulgarian: "прозорец", english: "window", pronunciation: "pro-zo-rets"},
            {bulgarian: "какво", english: "what", pronunciation: "kak-vo"},
            {bulgarian: "къща", english: "house", pronunciation: "kash-ta"},
            {bulgarian: "вода", english: "water", pronunciation: "vo-da"},
            {bulgarian: "време", english: "time", pronunciation: "vre-me"},
            {bulgarian: "добър", english: "good", pronunciation: "do-bar"},
            {bulgarian: "голям", english: "big", pronunciation: "go-lyam"},
            {bulgarian: "малък", english: "small", pronunciation: "ma-lak"}
        ];
        
        let currentWordIndex = 0;
        
        function checkAnswer() {
            const userAnswer = document.getElementById('answer-input').value.trim().toLowerCase();
            const correctAnswer = words[currentWordIndex].english.toLowerCase();
            const feedback = document.getElementById('feedback');
            
            if (userAnswer === correctAnswer) {
                feedback.innerHTML = '✅ Correct! Well done!';
                feedback.style.color = '#90EE90';
            } else {
                feedback.innerHTML = `❌ Incorrect. The answer is: ${words[currentWordIndex].english}`;
                feedback.style.color = '#FFB6C1';
            }
        }
        
        function nextWord() {
            currentWordIndex = (currentWordIndex + 1) % words.length;
            const word = words[currentWordIndex];
            
            document.getElementById('current-word').textContent = word.bulgarian;
            document.getElementById('current-pronunciation').textContent = word.pronunciation;
            document.getElementById('answer-input').value = '';
            document.getElementById('feedback').innerHTML = '';
        }
        
        // Test if Bulgarian characters display correctly
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ DOM loaded, testing Bulgarian characters...');
            console.log('Bulgarian word test: прозорец');
            console.log('If you see Cyrillic characters above, encoding is working!');
        });
    </script>
</body>
</html>
