<!DOCTYPE html>
<html>
<head>
    <title>🔧 Game Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        .error { background: #ffeeee; color: red; }
        .success { background: #eeffee; color: green; }
    </style>
</head>
<body>
    <h1>🔧 Game Test Page</h1>
    
    <div class="test-section">
        <h2>📊 User Info</h2>
        <div class="result">
            <strong>User:</strong> {{ user }}<br>
            <strong>Authenticated:</strong> {{ user.is_authenticated }}<br>
            <strong>Preferred Language:</strong> {{ user.profile.preferred_language }}
        </div>
    </div>
    
    <div class="test-section">
        <h2>🎮 Game Config Test</h2>
        <div class="result">
            <strong>Set ID:</strong> 33<br>
            <strong>Game Type:</strong> words<br>
            <strong>Difficulty:</strong> easy
        </div>
        <button onclick="testGameAPI()">🧪 Test Game API</button>
        <div id="api-result" class="result">Click button to test API...</div>
    </div>
    
    <div class="test-section">
        <h2>🎯 Direct Game Test</h2>
        <div id="game-area">
            <div id="question">Loading...</div>
            <input type="text" id="answer" placeholder="Enter translation..." onkeypress="if(event.key==='Enter') checkAnswer()">
            <button onclick="loadQuestion()">🔄 Load Question</button>
            <button onclick="checkAnswer()">✅ Check Answer</button>
            <button onclick="nextQuestion()">⏭️ Next Question</button>
        </div>
        <div id="game-result" class="result">Ready to test...</div>
    </div>

    <div class="test-section">
        <h2>🎮 Full Game Simulation</h2>
        <div id="full-game-area">
            <div><strong>Lives:</strong> <span id="lives">3</span></div>
            <div><strong>Score:</strong> <span id="score">0</span></div>
            <div><strong>Question:</strong> <span id="full-question">Click Start Game</span></div>
            <input type="text" id="full-answer" placeholder="Enter translation..." onkeypress="if(event.key==='Enter') submitFullAnswer()">
            <button onclick="startFullGame()">🎮 Start Game</button>
            <button onclick="submitFullAnswer()">✅ Submit</button>
            <button onclick="useHint()">💡 Hint</button>
        </div>
        <div id="full-game-result" class="result">Click Start Game to begin...</div>
    </div>

    <script>
        // Test variables
        let currentQuestion = null;
        
        // Test the API
        async function testGameAPI() {
            console.log('Testing game API...');
            const resultDiv = document.getElementById('api-result');
            resultDiv.textContent = 'Testing API...';
            
            try {
                const response = await fetch('/api/game-content/?set_id=33&type=words&limit=3');
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('API Response:', data);
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ API Success!</strong><br>
                    Items: ${data.items ? data.items.length : 0}<br>
                    Set: ${data.set_name}<br>
                    Type: ${data.content_type}<br>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                // Store first question for testing
                if (data.items && data.items.length > 0) {
                    currentQuestion = data.items[0];
                    document.getElementById('question').textContent = 
                        `Translate: "${currentQuestion.english}"`;
                }
                
            } catch (error) {
                console.error('API Error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ API Error!</strong><br>
                    ${error.message}<br>
                    Check console for details.
                `;
            }
        }
        
        // Load a question
        async function loadQuestion() {
            await testGameAPI();
        }
        
        // Check answer
        function checkAnswer() {
            const answer = document.getElementById('answer').value.trim().toLowerCase();
            const resultDiv = document.getElementById('game-result');
            
            if (!currentQuestion) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ No question loaded. Click "Load Question" first.';
                return;
            }
            
            const correct = currentQuestion.target.toLowerCase();
            
            if (answer === correct) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ Correct!</strong><br>
                    "${currentQuestion.english}" = "${currentQuestion.target}"<br>
                    Pronunciation: ${currentQuestion.pronunciation}
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ Incorrect!</strong><br>
                    Your answer: "${answer}"<br>
                    Correct answer: "${currentQuestion.target}"<br>
                    Pronunciation: ${currentQuestion.pronunciation}
                `;
            }
            
            // Clear input
            document.getElementById('answer').value = '';
        }
        
        // Full game variables
        let gameQuestions = [];
        let currentQuestionIndex = 0;
        let lives = 3;
        let score = 0;
        let currentFullQuestion = null;

        // Next question for simple test
        function nextQuestion() {
            if (gameQuestions.length > 0) {
                currentQuestionIndex = (currentQuestionIndex + 1) % gameQuestions.length;
                currentQuestion = gameQuestions[currentQuestionIndex];
                document.getElementById('question').textContent =
                    `Translate: "${currentQuestion.english}"`;
                document.getElementById('answer').value = '';
                document.getElementById('game-result').textContent = 'Enter your answer...';
                document.getElementById('game-result').className = 'result';
            }
        }

        // Start full game
        async function startFullGame() {
            try {
                const response = await fetch('/api/game-content/?set_id=33&type=words&limit=10');
                const data = await response.json();

                if (data.success && data.items) {
                    gameQuestions = data.items;
                    currentQuestionIndex = 0;
                    lives = 3;
                    score = 0;

                    updateFullGameDisplay();
                    loadFullQuestion();

                    document.getElementById('full-game-result').innerHTML =
                        '<strong>✅ Game Started!</strong><br>Translate the Bulgarian words to English.';
                    document.getElementById('full-game-result').className = 'result success';
                } else {
                    throw new Error('Failed to load game questions');
                }
            } catch (error) {
                document.getElementById('full-game-result').innerHTML =
                    `<strong>❌ Error:</strong> ${error.message}`;
                document.getElementById('full-game-result').className = 'result error';
            }
        }

        // Load current question for full game
        function loadFullQuestion() {
            if (currentQuestionIndex < gameQuestions.length) {
                currentFullQuestion = gameQuestions[currentQuestionIndex];
                document.getElementById('full-question').textContent =
                    `"${currentFullQuestion.target}" (${currentFullQuestion.pronunciation})`;
                document.getElementById('full-answer').value = '';
                document.getElementById('full-answer').focus();
            } else {
                // Game completed
                document.getElementById('full-question').textContent = 'Game Completed!';
                document.getElementById('full-game-result').innerHTML =
                    `<strong>🎉 Game Completed!</strong><br>Final Score: ${score}/${gameQuestions.length}`;
                document.getElementById('full-game-result').className = 'result success';
            }
        }

        // Submit answer for full game
        function submitFullAnswer() {
            if (!currentFullQuestion) return;

            const answer = document.getElementById('full-answer').value.trim().toLowerCase();
            const correct = currentFullQuestion.english.toLowerCase();

            if (answer === correct) {
                score++;
                document.getElementById('full-game-result').innerHTML =
                    `<strong>✅ Correct!</strong><br>"${currentFullQuestion.target}" = "${currentFullQuestion.english}"`;
                document.getElementById('full-game-result').className = 'result success';

                // Move to next question after delay
                setTimeout(() => {
                    currentQuestionIndex++;
                    loadFullQuestion();
                    updateFullGameDisplay();
                }, 1500);

            } else {
                lives--;
                document.getElementById('full-game-result').innerHTML =
                    `<strong>❌ Incorrect!</strong><br>Your answer: "${answer}"<br>Correct: "${currentFullQuestion.english}"`;
                document.getElementById('full-game-result').className = 'result error';

                if (lives <= 0) {
                    document.getElementById('full-question').textContent = 'Game Over!';
                    document.getElementById('full-game-result').innerHTML +=
                        `<br><strong>💀 Game Over!</strong><br>Final Score: ${score}/${currentQuestionIndex + 1}`;
                } else {
                    // Move to next question after delay
                    setTimeout(() => {
                        currentQuestionIndex++;
                        loadFullQuestion();
                        updateFullGameDisplay();
                    }, 2000);
                }
            }

            updateFullGameDisplay();
        }

        // Use hint
        function useHint() {
            if (!currentFullQuestion) return;

            const english = currentFullQuestion.english;
            const hint = english.substring(0, 2) + '...';

            document.getElementById('full-game-result').innerHTML =
                `<strong>💡 Hint:</strong> ${hint}<br>Example: ${currentFullQuestion.example_english}`;
            document.getElementById('full-game-result').className = 'result';
        }

        // Update display
        function updateFullGameDisplay() {
            document.getElementById('lives').textContent = lives;
            document.getElementById('score').textContent = score;
        }

        // Auto-test on page load
        window.addEventListener('load', function() {
            console.log('Test page loaded');
            setTimeout(testGameAPI, 1000);
        });
    </script>
</body>
</html>
