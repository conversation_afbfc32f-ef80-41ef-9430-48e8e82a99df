{% extends 'base.html' %}
{% load static %}

{% block title %}{{ word_set.name }} - {{ language.name }} - {{ site_settings.site_name }}{% endblock %}

{% block extra_css %}
<style>
.study-container {
    min-height: 100vh;
    padding: 100px 0 50px;
}

.study-card {
    background: rgba(15, 15, 35, 0.95);
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 50px rgba(139, 92, 246, 0.2);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.study-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(251, 191, 36, 0.02), transparent);
    animation: shimmer 10s ease-in-out infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.word-card {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(139, 92, 246, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.word-card:hover {
    border-color: var(--gold);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
}

.word-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.english-word {
    font-size: 1.3rem;
    color: var(--text-light);
    font-weight: 600;
}

.target-word {
    font-size: 1.5rem;
    color: var(--gold);
    font-weight: 700;
}

.pronunciation {
    color: var(--text-muted);
    font-style: italic;
    margin-bottom: 1rem;
}

.example-section {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
    margin-top: 1rem;
}

.example-label {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.example-text {
    color: var(--text-light);
    line-height: 1.5;
}

.set-header {
    text-align: center;
    margin-bottom: 2rem;
}

.set-title {
    font-size: 2.5rem;
    color: var(--gold);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.set-description {
    color: var(--text-muted);
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.set-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    color: var(--gold);
    font-weight: 700;
    display: block;
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.difficulty-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    display: inline-block;
    margin-bottom: 1rem;
}

.difficulty-easy {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid #22c55e;
}

.difficulty-medium {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border: 1px solid #fbbf24;
}

.difficulty-hard {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid #ef4444;
}

.btn-back {
    background: transparent;
    border: 2px solid var(--gold);
    color: var(--gold);
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 2rem;
}

.btn-back:hover {
    background: var(--gold);
    color: var(--bg-dark);
    text-decoration: none;
    transform: translateY(-2px);
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn-action {
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 1rem;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.4);
    color: white;
    text-decoration: none;
}

.btn-action.secondary {
    background: linear-gradient(45deg, rgba(251, 191, 36, 0.8), rgba(245, 158, 11, 0.8));
}

.words-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1rem;
}

@media (max-width: 768px) {
    .words-grid {
        grid-template-columns: 1fr;
    }
    
    .set-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Navigation Bar -->
{% include 'includes/navbar.html' %}

<div class="study-container">
    <div class="container">
        <!-- Back Button -->
        <a href="{% url 'core:study_words' %}" class="btn-back">
            <i class="fas fa-arrow-left me-2"></i>Back to Word Sets
        </a>

        <!-- Set Header -->
        <div class="study-card">
            <div class="set-header">
                <h1 class="set-title">{{ word_set.name }}</h1>
                <span class="difficulty-badge difficulty-{{ word_set.difficulty }}">
                    {{ word_set.difficulty }}
                </span>
                {% if word_set.description %}
                <p class="set-description">{{ word_set.description }}</p>
                {% endif %}
                
                <div class="set-stats">
                    <div class="stat-item">
                        <span class="stat-number">{{ words.count }}</span>
                        <span class="stat-label">Words</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ language.flag_emoji }}</span>
                        <span class="stat-label">{{ language.name }}</span>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{% url 'core:flashcard_practice' %}?type=words&set={{ word_set.id }}" class="btn-action">
                    <i class="fas fa-cards-blank me-2"></i>Practice Flashcards
                </a>
                <a href="{% url 'core:roguelike_game' %}?difficulty=easy&type=words&set={{ word_set.id }}&randomize=true" class="btn-action secondary">
                    <i class="fas fa-gamepad me-2"></i>Play Game
                </a>
            </div>
        </div>

        <!-- Words List -->
        {% if words %}
        <div class="study-card">
            <h2 class="text-center text-white mb-4">📚 Vocabulary List</h2>
            <div class="words-grid">
                {% for word in words %}
                <div class="word-card">
                    <div class="word-header">
                        <div class="english-word">{{ word.english_word }}</div>
                        <div class="target-word">{{ word.target_word }}</div>
                    </div>
                    
                    {% if word.pronunciation %}
                    <div class="pronunciation">/{{ word.pronunciation }}/</div>
                    {% endif %}
                    
                    {% if word.example_sentence_english or word.example_sentence_target %}
                    <div class="example-section">
                        {% if word.example_sentence_english %}
                        <div class="example-label">Example (English):</div>
                        <div class="example-text">{{ word.example_sentence_english }}</div>
                        {% endif %}
                        
                        {% if word.example_sentence_target %}
                        <div class="example-label">Example ({{ language.name }}):</div>
                        <div class="example-text">{{ word.example_sentence_target }}</div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        {% else %}
        <div class="study-card">
            <div class="text-center text-muted">
                <i class="fas fa-book-open fa-3x mb-3"></i>
                <h3>No Words Available</h3>
                <p>This word set doesn't contain any words yet.</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
